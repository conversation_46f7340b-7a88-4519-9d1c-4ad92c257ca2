import { request,qs } from '@weapp/utils';

export const checkPermission = (data: object) => {
  return request({
    url: '/api/auth/menu/check-permission',
    method: 'post', 
    data
  });
}

export const checkTenant = (data: object) => {
  return request({
    url: '/api/fna/expense/userData/tenantCheck',
    method: 'get', 
    data
  });
}


//商旅单点登录
export const getSSO = (data: object) => {
  return request({
    url: 'http://127.0.0.1:4523/m1/1216370-0-default/api/fna/getSSO',
    method: 'get', 
    params: data
  });
}

export const checkDetach = (data: object) => {
  return request({
      url: '/api/fna/menu/check-detach',
      method: 'GET',
      params:data,
  })
}
export const permissionCheckPC = (data: object) => {
  return request({
      url: '/api/fna/expense/userData/permissionCheck',
      method: 'GET',
      params:data,
  })
}



