import { request } from '@weapp/utils';
import { IProviderSsoLinkProps, IPriceCompareListProps, IProviderListState } from './type';

// 是否开启比价
export const getOpenPriceCompareReq = (params: any) => {
  return request({
    url: `/api/fnatravel/v1/priceCompare/openPriceCompare`,
    method: 'get',
    params
  });
};
// 获取服务商列表
export const getProviders = (params: IProviderListState) => {
  return request({
    url: `/api/fnatravel/provider/tabPage`,
    method: 'get',
    params
  });
};
// 获取服务商跳转链接
export const getProviderSsoLink = (data: IProviderSsoLinkProps) => {
  return request({
    url: `/api/fnatravel/provider/ssoLink`,
    method: 'post',
    data
  });
};
export const getPriceCompareList = (data: IPriceCompareListProps) => {
  return request({
    url: `/api/fnatravel/v1/priceCompare/getPriceCompareList`,
    method: 'post',
    data
  });
};