export interface IProviderSsoLinkProps {
  pageType: string; // 页面类型 ORDER 订单 BOOKING 预订
  isMobile: boolean; // 是否是移动端
  pallet: string; //平台标识 1.服务商标签页面返回数据获取 2.比价数据列表返回数据获取
  trafficType?: string; //移动端H5使用 出行方式 服务商标签页面返回数据trafficTypeList中获取
  bizNo?: string; //页面单号  pageType参数为BOOKING 预订时,必填
  journeyNo?: string; //行程单号  明细行rowId
  depCity?: string;
  arrCity?: string;
  startDate?: string;
  endDate?: string;
  tripWay?: number; //1 单程 2往返 目前写死固定值 1
}
export interface IPriceCompareListProps {
  requestId: string; //流程id
  rowId?: string; // 行id
  startDate: string; //开始时间
  endDate?: string; //结束时间
  productType: number; //产品类型 1机票 2酒店 3火车\
  from?: string; //出发地
  to?: string; //目的地
  refreshCacheData?: boolean; // 刷新缓存数据
}
export interface IProviderListState {
  deviceType: string;
  requestId?: string;
  trafficType?: string;
}
