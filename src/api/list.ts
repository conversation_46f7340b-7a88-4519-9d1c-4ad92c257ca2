import { request } from '@weapp/utils';

// 获取列表配置
export const getListConfig = (params: any) => {
  return request({
    url: '/api/fs/workflow/core/list/getListConfig',
    method: 'get',
    params: { // todo: 这里的参数暂时写死的
      initList: true,
      userid: '5111597426042878265',
      tenantkey: 'TSHY6DTU0Z',
      ...params,
    },
  });
}

// 获取列表右上角按钮配置信息
export const getListButtonConfig = (params: any) => {
  return request({
    url: '/api/fs/workflow/core/list/getListButton',
    method: 'get',
    params: { // todo: 这里的参数暂时写死的
      userid: '5111597426042878265',
      tenantkey: 'TSHY6DTU0Z',
      ...params,
    },
  });
}

// 获取左侧树数据
export const getListLeftTree = (params: any) => {
  switch(params.dimension) {
    case 'doing': return request({
      url: '/api/fs/workflow/core/list/getTodoLeftTree',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'done': return request({
      url: '/api/fs/workflow/core/list/getDoneLeftTree',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'mine': return request({
      url: '/api/fs/workflow/core/list/getMineLeftTree',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'draft': return request({
      url: '/api/fs/workflow/core/list/getDraftLeftTree',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'subordinate': return request({
      url: '/api/fs/workflow/core/list/getSubordinateLeftTree',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'all': return request({
      url: '/api/fs/workflow/core/list/getAllLeftTree',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    default: return request({
      url: '/api/fs/workflow/core/list/getTodoLeftTree',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
  }
}

// 获取列表数量统计: 用于Menu组件的合计以及左侧树的合计
export const getListCount = (params: any) => {
  switch(params.dimension) {
    case 'doing': return request({
      url: '/api/fs/workflow/core/list/getTodoCount',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'done': return request({
      url: '/api/fs/workflow/core/list/getDoneCount',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'mine': return request({
      url: '/api/fs/workflow/core/list/getMineCount',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'draft': return request({
      url: '/api/fs/workflow/core/list/getDraftCount',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'subordinate': return request({
      url: '/api/fs/workflow/core/list/getSubordinateCount',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    case 'all': return request({
      url: '/api/fs/workflow/core/list/getAllCount',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
    default: return request({
      url: '/api/fs/workflow/core/list/getTodoCount',
      method: 'get',
      params: { // todo: 这里的参数暂时写死的
        userid: '5111597426042878265',
        tenantkey: 'TSHY6DTU0Z',
        ...params,
      },
    });
  }
}

// 获取高级搜索数据
export const getListSearchAd = (params: any) => {
  return request({
    url: '/api/fs/workflow/core/list/getListSearchAd',
    method: 'get',
    params: { // todo: 这里的参数暂时写死的
      userid: '5111597426042878265',
      tenantkey: 'TSHY6DTU0Z',
      ...params,
    },
  });
}

// 获取饼图的审批统计信息
export const getApprovalStatis = (params: any) => {
  return request({
    url: '/api/fs/workflow/core/list/getApprovalStatis',
    method: 'get',
    params: { // todo: 这里的参数暂时写死的
      initList: true,
      userid: '5111597426042878265',
      tenantkey: 'TSHY6DTU0Z',
      ...params,
    },
  });
}


