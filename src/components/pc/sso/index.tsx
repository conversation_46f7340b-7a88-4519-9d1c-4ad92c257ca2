import React, { useEffect, useMemo, useCallback, useRef, useState, ReactNode } from "react";
import { toJS } from 'mobx';
import { weappFnaBsTravelClsPrefix } from "../../../constants";

import { Spin } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { getSSO } from '../../../api/pc/common/common';
import Unauthorized from '../common/unauthorized';
import {getUrlParam} from '../../../utils';

// import { BatchDialogRef, BatchDialogProps } from "./types";



const SSO = React.forwardRef<any, any>(
    (props, ref) => {
        const [loading, setLoading] = useState(true);
        const [hasRight, setHasRight] = useState('1');
        const [showMsg, setShowMeg ] = useState('');
        const {location} = props;
        const {search={}} = location;
        const routeParams = getUrlParam(search);
        const style = {width:'100%',height:'100%'};

        // useEffect(()=>{
        //     const getSSOFn = function (value: any) {
        //         getSSO(value).then((response: any) => {
        //             const { data, code } = response;
        //             const { url,errorInfo } = data;
        //             setTimeout(()=>{
        //                 console.log('response',response)
        //                 if (code === 200) {
        //                     if(url){
        //                         window.open(url);
        //                     }else{
        //                         setHasRight('-1');
        //                         setShowMeg(errorInfo);
        //                     }
        //                 } else {
        //                     console.log('errorInfo',errorInfo)
        //                     setHasRight('-1');
        //                     setShowMeg(errorInfo);
        //                 }
        //                 setLoading(false);
        //             },2000)
                    
                    
        //         }).catch((error: any) => {

        //         });
        //     }
        //     getSSOFn(routeParams);
        // },[routeParams])
        useEffect(()=>{
            let params = routeParams;
		
            var options = {
                url: "/api/fna/travel/ctrip/usersso/getSSOLink",
                data: params
            };
            var config = $.extend(true, { method: 'GET' }, options);
            var $form = $('<form target="down-file-iframe" method="' + config.method + '" />');
            $form.attr('action', config.url);
            // console.log('$',$.each(index,els))
            console.log('config.data',config)
            $.each(config.data, function (key, value) {
                var $input = $('<input type="hidden"/>').attr({
                    'name': key,
                    'value': value
                });
                $form.append($input)
            });
            $(document.body).append($form[0]);
            ($form[0] as any).submit();
        },[routeParams])


        return (
            <div className={`${weappFnaBsTravelClsPrefix}-sso`} style={style}>
                <Spin weId={`${props.weId || ''}_sgx3ii`} spinning={loading} globalLoading ></Spin>
                {!loading && <>
                    {hasRight === '1' && <div></div>}
                    {hasRight === '-1' && <Unauthorized weId={`${props.weId || ''}_9q0gwl`} msg={showMsg} />}
                </>}
            </div>
        )

    }
)

export default SSO;