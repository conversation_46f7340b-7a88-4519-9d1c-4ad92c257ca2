import React, { Suspense } from 'react';
import { Route, withRouter, RouteComponentProps } from 'react-router-dom';
import { observer, inject } from 'mobx-react';
import { middleware,getLabel } from '@weapp/utils';
import { fomatParentPath,setPageTitle } from '../../../utils';
import Loading from '../loading';
import { weappFnaBsTravelClsPrefix } from "../../../constants";
/* typescript interface */
import { MainProps } from './types';
/*
* 异步组件可以产生文件相互关联引用，请保持一个 import() 动态入口
* lib.js -> import('routes/demo') -> demo/index.tsx -> lib.js
*/

import { RouteSSO, RoutePriceComparison } from '../../../lib';
import './style/index.less';

@middleware('weappFnabstravel', 'Main')
@observer
class Main extends React.Component<MainProps> {

  render() {
    return (
      <div className={weappFnaBsTravelClsPrefix}>
        <Suspense weId={`${this.props.weId || ''}_owod8e`} fallback={<Loading weId={`${this.props.weId || ''}_45zgjl`}/>}>
          <RouteSSO weId={`${this.props.weId || ''}_2gk9lp`} />
          <RoutePriceComparison weId={`${this.props.weId || ''}_4h7hu3`} />
        </Suspense>
      </div>
    )
  }
}

export default withRouter(Main);

