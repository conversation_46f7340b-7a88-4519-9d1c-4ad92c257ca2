import React from 'react';
import { withRouter,RouteComponentProps } from 'react-router-dom';
import './style/index.less';
import { ua,getLabel } from '@weapp/utils';
import { Spin,Dialog  } from '@weapp/ui';
import { weappFnaBsTravelClsPrefix } from "../../../constants";

export interface WrapperProps extends RouteComponentProps {
  [_str:string]:any;
}

class Wrapper extends React.Component<WrapperProps,any> {
  state = {
    loading:false
  };

  componentDidMount(){
    this.setState({loading:true});
  }

  render() {
    return (
      <>
        <Spin weId={'_sdfaf'} spinning={this.state.loading} wrapperClassName={`${weappFnaBsTravelClsPrefix}-spin`}>
          {this.props.children}
        </Spin>
      </>
    )
  }
}

export default withRouter(Wrapper);