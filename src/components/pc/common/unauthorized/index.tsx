import { Empty, Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { PureComponent } from 'react';
import { withRouter } from 'react-router-dom';
import { weappFnaBsTUnauthorized } from "../../../../constants";

class Unauthorized extends PureComponent<any>{
    render() {
        const { className } = this.props;
        return (
            <div className={`${weappFnaBsTUnauthorized} ${className}`}>
                <Empty weId={`${this.props.weId || ''}_1njnbr`}
                    title={this.props.msg || getLabel('54812','对不起，您暂时没有权限！')}
                    image={<Icon weId={`${this.props.weId || ''}_4kgty9`} 
                    className={'unauthorized'}
                        name={this.props.icon || 'Icon-empty-No-permission'} />}
                    />
            </div>
        );
    }
}

export default withRouter(Unauthorized);