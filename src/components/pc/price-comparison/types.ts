export interface IBizFlightProps {
  departDate: string; // 出发日期，与出差审批单中日期一致
  aliBizTripFlightUrl: string; // 阿里商旅机票PC端跳转链接
  ctBizTripFlightUrl: string; // 携程商旅机票PC端跳转链接
  dtBizTripFlightUrl: string; // 同程商旅机票PC端跳转链接
}
// 飞机票
export interface AirListProps {
  logoSrc: string; // 航司logo
  airplaneName: string; // 航司名称
  departFlightNumber: string; // 航班号
  departTime: string; //出发时间
  departAirportName: string; //出发机场
  departTerminalName: string; //出发航站楼
  aircraftModel: string; //机型
  arriveTime: string; //到达时间
  arriveAirportName: string; //到达机场
  arriveTerminalName: string; //到达航站楼
  timeSpanMillis: number; //航班耗时（单位：ms）
  price: string; //票价
  spanDays: string; //跨天数 空或0 不跨天
  space: string; //舱位
  discount: string; //折扣
  platform: string; //平台：aliBizTrip 阿里商旅；ctBizTrip：携程商旅；dtBizTrip：同程商旅；
}
// 酒店
export interface IHotelListProps {
  hotelImg: string; // 酒店图片
  hotelName: string; //酒店名称
  hotelAddress: string;
  hotelAddressPoiDis: number; //酒店距离关键词地点距离（单位：米）
  hotelAddressPoiDisText: string; //酒店距离关键词地点文案
  hotelScore: string; //酒店评分
  hotelPrice: string; //酒店价格
  estimate: string; //酒店整体评价
  tags: string[]; //酒店标签
  tmpStarStr: string; //星级，2星：经济型；3星：舒适型；4星：高档型；5星：豪华型
  platform: string; //平台：aliBizTrip 阿里商旅；ctBizTrip：携程商旅；dtBizTrip：同程商旅；
}