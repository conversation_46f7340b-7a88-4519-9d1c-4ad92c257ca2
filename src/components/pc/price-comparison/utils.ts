import React, { useState, useRef } from 'react';
import { Dialog } from '@weapp/ui';
import { getLabel, qs } from '@weapp/utils';
import cloneDeep from 'lodash-es/cloneDeep';
import { getPriceCompareList, getProviders, getProviderSsoLink } from '../../../api/common/price-comparison';
import { IPriceCompareListProps, IProviderSsoLinkProps } from '../../../api/common/price-comparison/type';

const { message } = Dialog;
const messageTip = (type: 'info' | 'error' | 'success', content: any) => {
  message({ type, content });
};
export const getUrlParams = () => {
  const search = window.location.search;
  return qs.parse(search, { ignoreQueryPrefix: true });
};
const getSsoLink = async (item: any, channel: string, isMobile: boolean, isReturn: boolean) => {
  const errTip = (msg?: string) => messageTip('error', msg || getLabel('268008', '服务商跳转信息获取失败'));
  try {
    const urlParams: any = getUrlParams();
    let params: IProviderSsoLinkProps = {
      pageType: urlParams?.pageType,
      isMobile,
      pallet: item.platform,
      bizNo: urlParams?.requestId ?? '',
      journeyNo: urlParams?.rowId ?? '',
      trafficType: channel === 'bizHotel' ? 'HOTEL' : 'TICKET',
      tripWay: 1, //1 单程 2往返 目前写死固定值 1
    };
    if (params.trafficType === 'TICKET' || params.trafficType === 'TRAIN') {
      params = {
        ...params,
        depCity: isReturn ? urlParams?.mdd : urlParams?.cfd,
        arrCity: isReturn ? urlParams?.cfd : urlParams?.mdd,
        startDate: urlParams?.cfrq,
        endDate: urlParams?.fhrq,
      }
    } else if (params.trafficType === 'HOTEL') {
      params = {
        ...params,
        arrCity: urlParams?.rzcs,
        startDate: urlParams?.rzrq,
        endDate: urlParams?.ldrq,
      }
    }
    const response = await getProviderSsoLink(params);
    if (+response.code === 200) {
      return response.data?.linkUrl;
    }
    throw new Error(response.msg);
  } catch (error) {
    // @ts-ignore
    errTip(error?.message);
  } finally {
  }
};
export const isWeapp = (): boolean => {
  const ua = window.weappUtils.ua;
  return ua?.browser === 'Weapp';
};
export const TravelOrder = async (item: any, channel: string, isMobile: boolean, isReturn: boolean) => {
  try {
    const linkUrl = await getSsoLink(item, channel, isMobile, isReturn);
    if (linkUrl) {
      // app内打开
      if (isWeapp() && window.weapp) {
        window.weapp.invoke('openLink', {
          url: linkUrl,
          openType: 2,
          hideNavegation: 1,
        });
      } else {
        window.open(linkUrl, '_blank');
      }
    }
  } catch (error) {}
};
export const handleChangeOrderInfo = (channel: string, airOrder: any, hotelOrder: any, key: string, value?: any) => {
  if (channel === 'bizFlight') {
    switch (key) {
      case 'hasReturn':
        const urlSearchParams: any = getUrlParams();
        // 互换出发和返程
        return {
          ...airOrder,
          from: airOrder.to,
          to: airOrder.from,
          // 重置开始和结束
          departDate: urlSearchParams.cfrq,
          returnDate: urlSearchParams?.fhrq,
          hasReturn: !airOrder.hasReturn,
        };
      default:
        return {
          ...airOrder,
          [key]: value,
        };
    }
  } else {
    // 单独处理价格筛选
    if (key.indexOf('price') > -1) {
      if (Object.prototype.toString.call(value) === '[object Object]' && 'min' in value && 'max' in value) {
        // 自带min和max的价格区间，直接赋值，移动端滑块有这种场景
        let extraProps: any = {
          sortKey: 'price',
        };
        // 重置操作  重置为综合排序
        if (!value.min && !value.max) {
          extraProps = {
            sortKey: 'normal'
          };
        }
        return {
          ...hotelOrder,
          ...extraProps,
          price: value,
        };
      } else {
        const priceKey = key.split('price.')[1];
        return {
          ...hotelOrder,
          price: {
            ...hotelOrder.price,
            [priceKey]: value,
          },
        };
      }
    }
    return {
      ...hotelOrder,
      [key]: value,
    };
  }
};

export const handleGetData = async (channel: string, airOrder: any, hotelOrder: any, refreshCacheData?: boolean) => {
  const errTip = (msg?: string) => messageTip('error', msg || getLabel('268017', '获取比价列表失败'));
  try {
    const pageParams: any = getUrlParams();
    let params: any = {
      requestId: pageParams.requestId,
      rowId: pageParams.rowId,
      productType: +pageParams.productType,
    };
    if (channel === 'bizHotel') {
      params = {
        ...params,
        startDate: hotelOrder.checkInDate,
        endDate: hotelOrder.checkOutDate,
        keyword: hotelOrder.keyword,
        arriveCityName: pageParams.rzcs,
      };
    }
    if (channel === 'bizFlight') {
      params = {
        ...params,
        returnTrip: airOrder.hasReturn,
        startDate: airOrder.departDate,
        endDate: airOrder.returnDate,
        departCityName: pageParams.cfd,
        arriveCityName: pageParams.mdd,
      };
    }
    if (refreshCacheData) {
      params.refreshCacheData = true;
    }
    const response: any = await getPriceCompareList(params);
    return response;
  } catch (error) {
    // @ts-ignore
    errTip(error?.message);
  }
};

export const useGetState = (initVal: any) => {
  const [state, setState] = useState(initVal);
  const ref = useRef(initVal);
  const setStateCopy = (newVal: any) => {
    ref.current = newVal;
    setState(newVal);
  };
  const getState = () => {
    return ref.current;
  };
  return [state, setStateCopy, getState];
};

export const TAB_LIST = () => [
  {
    id: 'bizFlight',
    content: getLabel('268002', '订机票'),
  },
  {
    id: 'bizHotel',
    content: getLabel('268003', '找酒店'),
  },
];

export const formatDate = (ms: number, client?: string) => {
  if (!ms) {
    return '';
  }
  const days = Math.floor(ms / 86400000);
  const hours = Math.floor((ms % 86400000) / 3600000);
  const minutes = Math.floor((ms % 3600000) / 60000);
  let result = '';
  if (days) {
    result += `${days}${client === 'MOBILE' ? 'd' : getLabel('41389', '天')}`;
  }
  if (hours) {
    result += `${hours}${client === 'MOBILE' ? 'h' : getLabel('41390', '小时')}`;
  }
  if (minutes) {
    result += `${minutes}${client === 'MOBILE' ? 'm' : getLabel('268016', '分钟')}`;
  }
  return result;
};
export const AIR_ORDER_INFO = () => ({
  from: '', // 出发地
  to: '', // 目的地
  departDate: '', // 出发日期，与出差审批单中日期一致
  returnDate: '', // 返程日期
  isReturn: false,
  hasReturn: false,
  sortKey: 'price', // 排序字段 price timeSpanMillis
  sortWay: 'asce', // 排序方式 asce desc
});
export const HOTEL_ORDER_INFO = () => ({
  to: '', // 目的地
  keyword: '', // 位置/酒店名
  checkInDate: '', // 入住时间
  checkOutDate: '', // 离店时间
  sortKey: 'normal', // 排序字段 price normal
  sortWay: 'asce', // 排序方式  asce desc
  price: {
    min: undefined,
    max: undefined,
  }, // 价格区间
});
export const H5_HOTEL_ORDER_INFO = () => ({
  to: '', // 目的地
  keyword: '', // 位置/酒店名
  checkInDate: '', // 入住时间
  checkOutDate: '', // 离店时间
  sortKey: 'normal', // 排序字段 normal:综合排序 price:价格排序
  sortWay: 'normal', // 排序方式 normal asce desc
  price: {
    min: undefined,
    max: undefined,
    selectId: ''
  }, // 价格区间
});
// 获取符合价格区间的数据
export const getValidPriceList = (list: any, orderInfo: any) => {
  const { price = {} } = orderInfo;
  let _result = cloneDeep(list);
  // 都为0 不参与筛选 直接返回
  if (price.min === 0 && price.max === 0) {
    return _result;
  }
  if (price.min) {
    _result = [..._result].filter((item: any) => {
      return Number(item.lowestInfo.price) >= +price.min;
    });
  }
  if (price.max || price.max === 0) {
    _result = [..._result].filter((item: any) => {
      return Number(item.lowestInfo.price) <= +price.max;
    });
  }
  return _result;
};
export const getSort = (orderInfo: any, channel: string) => {
  const { sortWay } = orderInfo;
  // 酒店改造  酒店筛选下默认都是价格排序
  let sortKey = channel === 'bizHotel' ? 'price' : orderInfo.sortKey
  let sort = (a: any, b: any) => a[sortKey] - b[sortKey];
  if (sortWay === 'desc') {
    sort = (a: any, b: any) => b[sortKey] - a[sortKey];
  }
  return sort;
};

export const travelWay: any = {
  1: 'TICKET',
  2: 'HOTEL',
  3: 'TRAIN',
};
