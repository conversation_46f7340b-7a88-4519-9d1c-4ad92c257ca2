/**
 * 差旅比价页
 */
import {
  Spin,
  Input,
  Button,
  Icon,
  CorsComponent,
  DatePicker,
  Table,
  ITableColumn,
  ITablePaginationType,
  Popover,
  Dialog,
  Empty,
} from '@weapp/ui';
import { getLabel, qs, setTitle } from '@weapp/utils';
import React, { useEffect, useState } from 'react';
import isEmpty from 'lodash-es/isEmpty';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import { weappFnaBsTravelClsPrefix } from '../../../constants';
import { getProviders } from '../../../api/common/price-comparison';
import {
  TravelOrder,
  handleChangeOrderInfo,
  handleGetData,
  useGetState,
  TAB_LIST,
  formatDate,
  AIR_ORDER_INFO,
  HOTEL_ORDER_INFO,
  getSort,
  travelWay,
  getValidPriceList,
} from './utils';
import TransitionEle from '../../common/transition';
import LazyLoadImg from '../../common/lazyLoadImg';
import './style/index.less';

const cls = `${weappFnaBsTravelClsPrefix}-priceComparison`;
const { message } = Dialog;
const { globalSpin } = Spin;
const { InputNumber } = Input;
const initial_page_info = () => ({
  current: 1,
  pageSize: 10,
  total: 0,
  paginationType: 'part',
});
// 路由信息参考-飞机 /sp/fnabstravel/priceComparison?pageType=BOOKING&productType=1&rowId=471592477075144598&requestId=999545121601830913&dcwf=0&cfrq=2024-05-17&fhrq=2024-05-20
// 路由信息参考 - 酒店 /sp/fnabstravel/priceComparison?pageType=BOOKING&rzcs=北京&rzrq=2024-05-20&ldrq=2024-05-24&requestId=999545121601830913&productType=2&rowId=471592474225135666
export interface IProps {
  weId?: string;
}
const PriceComparison: React.ComponentType<IProps> = (props: IProps) => {
  const [airOrder, setAirOrder, getAirOrder] = useGetState(AIR_ORDER_INFO()); // 订机票
  const [hotelOrder, setHotelOrder, getHotelOrder] = useGetState(HOTEL_ORDER_INFO()); // 订酒店
  const [pending, setPending] = useState(false);
  const [ordering, setOrdering] = useState(false);
  const [channel, setChannel, getChannel] = useGetState('bizFlight'); // bizFlight bizHotel
  const [isScroll, setIsScroll, getIsScroll] = useGetState(false);
  const [listData, setListData, getListData] = useGetState([]);
  const [originListData, setOriginListData, getOriginListData] = useGetState([]);
  const [pageInfo, setPageInfo, getPageInfo] = useGetState(initial_page_info());
  const [provider, setProvider, getProvider] = useGetState([]);
  const [errText, setErrText] = useState('');
  const [lastTime, setLastTime] = useState(''); // 最后一次更新时间
  // const [mockVisible, setMockVisible] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');

  useEffect(() => {
    setTitle({ title: getLabel('268026', '商旅比价') });
    const scrollContainer = document.querySelector(`.${cls}`);
    if (scrollContainer) {
      scrollContainer?.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (scrollContainer) {
        scrollContainer?.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);
  const handleScroll = (e: any) => {
    const scrollTop = e.target.scrollTop;
    const _isScroll = scrollTop > 0;
    if (getIsScroll() !== _isScroll) {
      setIsScroll(_isScroll);
    }
  };
  useEffect(() => {
    setParams();
    getProviderReq();
    getData();
  }, []);
  const messageTip = (type: 'info' | 'error' | 'success', content: any) => {
    message({ type, content });
  };
  const getUrlParams = () => {
    const search = window.location.search;
    return qs.parse(search, { ignoreQueryPrefix: true });
  };
  const setParams = () => {
    const urlSearchParams: any = getUrlParams();
    // 飞机票
    if (+urlSearchParams.productType === 1) {
      setChannel('bizFlight');
      setAirOrder({
        ...airOrder,
        from: urlSearchParams?.cfd,
        to: urlSearchParams?.mdd,
        departDate: urlSearchParams?.cfrq,
        returnDate: urlSearchParams?.fhrq,
        isReturn: +urlSearchParams?.dcwf === 2,
      });
    } else if (+urlSearchParams.productType === 2) {
      // 酒店票
      setChannel('bizHotel');
      setHotelOrder({
        ...hotelOrder,
        to: urlSearchParams?.rzcs,
        checkInDate: urlSearchParams?.rzrq,
        checkOutDate: urlSearchParams?.ldrq,
      });
    }
  };
  const getData = async (isRefresh?: boolean) => {
    try {
      if (pending) {
        return;
      }
      setPending(true);
      setListData([]);
      const _channel = getChannel();
      const response = await handleGetData(_channel, getAirOrder(), getHotelOrder(), isRefresh);
      switch (response.code) {
        case 200:
          const { flightList, hotelList, lastTime } = response.data;
          setOriginListData(_channel === 'bizFlight' ? flightList : hotelList);
          sortList(_channel === 'bizFlight' ? flightList : hotelList);
          setLastTime(lastTime);
          break;
        case 269044:
        case 269045:
          setErrText(response.msg);
          break;
        default:
          break;
      }
    } catch (error) {
    } finally {
      setPending(false);
    }
  };
  // 处理排序
  const sortList = (list: any) => {
    const _channel = getChannel();
    const _order = _channel === 'bizFlight' ? getAirOrder() : getHotelOrder();
    // 如果设置了价格区间 先处理一遍范围内的价格
    let result = getValidPriceList(list, _order);
    if (_order.sortKey === 'normal') {
      // 综合排序不处理
    } else {
      result = [...result].sort((a: any, b: any) => {
        return getSort(_order, _channel)(a.lowestInfo, b.lowestInfo);
      });
    }
    setPageInfo({ ...initial_page_info(), pageSize: pageInfo.pageSize });
    setListData(result);
  };
  const getProviderById = (id: string) => {
    return getProvider().find((item: any) => item.id === id);
  };
  // 获取服务商
  const getProviderReq = async () => {
    const errTip = (msg?: string) => messageTip('error', msg || getLabel('267999', '获取服务商失败'));
    try {
      const pageParams: any = getUrlParams();
      const response = await getProviders({
        deviceType: 'PC',
        requestId: pageParams?.requestId,
        trafficType: travelWay[+pageParams?.productType],
      });
      if (+response.code === 200) {
        const _providers = response.data;
        setProvider(_providers || []);
        return;
      }
      throw new Error(response.msg);
    } catch (error) {
      // @ts-ignore
      errTip(error?.message);
    } finally {
    }
  };
  const changeOrderInfo = (key: string, value?: any) => {
    if (pending) {
      return;
    }
    const _channel = getChannel();
    const newData = handleChangeOrderInfo(_channel, getAirOrder(), getHotelOrder(), key, value);
    const setFunc = _channel === 'bizFlight' ? setAirOrder : setHotelOrder;
    const orderInfo = _channel === 'bizFlight' ? airOrder : hotelOrder;
    // 处理升降序
    if (key === 'sortKey' && orderInfo.sortKey === value) {
      newData.sortWay = orderInfo.sortWay === 'asce' ? 'desc' : 'asce';
    }
    setFunc(newData);
    const refreshKey = ['hasReturn', 'departDate', 'returnDate', 'checkInDate', 'checkOutDate', 'keyword'];
    if (refreshKey.includes(key)) {
      getData();
      return;
    }
    if (key === 'sortKey' || key.indexOf('price') > -1) {
      // 每次拿最原始的列表数据去排序
      sortList(getOriginListData());
    }
  };
  // 暂时不支持切换订机票和酒店
  const handleSetTab = (tab: string) => {
    setChannel(tab);
    setListData([]);
    getData();
  };
  // 筛选区
  const renderSection2 = () => {
    const renderAir = () => {
      const _urlParams = getUrlParams();
      return (
        <div className={`${cls}-section2-line2`}>
          <div className={`${cls}-section2-line2-item`}>
            <div className={`${cls}-section2-line2-item-label`}>{getLabel('268009', '出发地')}</div>
            <div className={`${cls}-section2-line2-item-main`}>{airOrder.from}</div>
          </div>
          <div
            className={`${cls}-section2-line2-item-change ${airOrder.hasReturn ? 'hasReturn' : ''} ${airOrder.isReturn ? 'isReturn' : ''}`}
            onClick={() => (airOrder.isReturn ? changeOrderInfo('hasReturn') : {})}
            // onClick={() => setMockVisible(true)}
          >
            <div className={`${cls}-section2-line2-item-change-icon`}>
              {/* @ts-ignore */}
              <Icon weId={`${props.weId || ''}_a33xqi`} name="Icon-Automated-workflow02" size="lg" />
            </div>
          </div>
          <div className={`${cls}-section2-line2-item`}>
            <div className={`${cls}-section2-line2-item-label`}>{getLabel('268010', '目的地')}</div>
            <div className={`${cls}-section2-line2-item-main`}>{airOrder.to}</div>
          </div>
          <div className={`${cls}-section2-line2-item-divide`}></div>
          <div className={`${cls}-section2-line2-item departDate`}>
            <div className={`${cls}-section2-line2-item-label`}>
              {airOrder.hasReturn ? getLabel('268392', '返程日期') : getLabel('148934', '出发日期')}
            </div>
            <div className={`${cls}-section2-line2-item-main`}>
              <DatePicker
                weId={`${props.weId || ''}_sgkl3r`}
                value={airOrder.hasReturn ? airOrder.returnDate : airOrder.departDate}
                // @ts-ignore
                allowClear={false}
                minDate={_urlParams?.cfrq || ('' as any)}
                maxDate={_urlParams?.fhrq || ('' as any)}
                onChange={value => changeOrderInfo(airOrder.hasReturn ? 'returnDate' : 'departDate', value)}
              />
            </div>
          </div>
        </div>
      );
    };
    const renderHotel = () => {
      return (
        <div className={`${cls}-section2-line2`}>
          <div className={`${cls}-section2-line2-item ${cls}-section2-line2-item-md`}>
            <div className={`${cls}-section2-line2-item-label`}>{getLabel('268018', '入住地/入住城市')}</div>
            <div className={`${cls}-section2-line2-item-main`}>{hotelOrder.to}</div>
          </div>
          <div className={`${cls}-section2-line2-item ${cls}-section2-line2-item-md mgl16`}>
            <div className={`${cls}-section2-line2-item-label`}>{getLabel('268019', '关键词（选填）')}</div>
            <div className={`${cls}-section2-line2-item-main needAnimate`}>
              <Input
                weId={`${props.weId || ''}_2zo3l2`}
                placeholder={getLabel('268020', '输入位置/酒店名')}
                // value={hotelOrder.keyword}
                className={`${cls}-section2-line2-item-main-input`}
                onPressEnter={(value: any) => changeOrderInfo('keyword', value)}
              />
            </div>
          </div>
          <div className={`${cls}-section2-line2-item-divide mgl24`}></div>
          <div className={`${cls}-section2-line2-item ${cls}-section2-line2-item-sm`}>
            <div className={`${cls}-section2-line2-item-label`}>{getLabel('268021', '入住时间')}</div>
            <div className={`${cls}-section2-line2-item-main needAnimate`}>
              <DatePicker
                weId={`${props.weId || ''}_sgkl3r`}
                value={hotelOrder.checkInDate}
                // @ts-ignore
                allowClear={false}
                disabled
              />
            </div>
          </div>
          <div className={`${cls}-section2-line2-item ${cls}-section2-line2-item-sm mgl16`}>
            <div className={`${cls}-section2-line2-item-label`}>{getLabel('268022', '退房时间')}</div>
            <div className={`${cls}-section2-line2-item-main needAnimate`}>
              <DatePicker
                weId={`${props.weId || ''}_ncm8f8`}
                value={hotelOrder.checkOutDate}
                // @ts-ignore
                allowClear={false}
                disabled
              />
            </div>
          </div>
          <div className={`${cls}-section2-line2-item-divide mgl24`}></div>
          <div className={`${cls}-section2-line2-item ${cls}-section2-line2-item-sm`}>
            <div className={`${cls}-section2-line2-item-label`}>{getLabel('268012', '价格')}</div>
            <div className={`${cls}-section2-line2-item-main`}>
              <InputNumber
                weId={`${props.weId || ''}_89ed8s`}
                placeholder={getLabel('41443', '请输入')}
                value={hotelOrder.price.low}
                onBlur={(value: any) => changeOrderInfo('price.min', value)}
                onPressEnter={(value: any) => changeOrderInfo('price.min', Number(value))}
                max={hotelOrder.price.max}
                min={0}
              />
              <div className={`${cls}-section2-line2-item-hDivide`}></div>
              <InputNumber
                weId={`${props.weId || ''}_6oogpe`}
                placeholder={getLabel('41443', '请输入')}
                value={hotelOrder.price.high}
                min={hotelOrder.price.min || 0}
                onBlur={(value: any) => changeOrderInfo('price.max', value)}
                onPressEnter={(value: any) => changeOrderInfo('price.max', Number(value))}
              />
            </div>
          </div>
        </div>
      );
    };
    return (
      <div className={`${cls}-commonSection ${cls}-section2 ${getChannel()}`}>
        {/* <div className={`${cls}-section2-line1`}>
          {TAB_LIST().map(item => (
            <div
              className={`${cls}-section2-line1-item ${getChannel() === item.id ? `${cls}-section2-line1-item-active` : ''}`}
              key={item.id}
              onClick={() => handleSetTab(item.id)}
            >
              {item.content}
            </div>
          ))}
        </div> */}
        <TransitionEle weId={`${props.weId || ''}_bv811x`} updateKey={getChannel()}>
          {getChannel() === 'bizFlight' ? renderAir() : renderHotel()}
        </TransitionEle>
      </div>
    );
  };
  const order = async (item: any) => {
    try {
      if (ordering) {
        return;
      }
      globalSpin.start();
      setOrdering(true);
      const _channel = getChannel();
      const _order = _channel === 'bizFlight' ? getAirOrder() : getHotelOrder();
      await TravelOrder(item, getChannel(), false, _order.hasReturn);
    } catch (error) {
    } finally {
      setOrdering(false);
      globalSpin.destroy();
    }
  };
  // const mockRender = () => {
  //   return (
  //     <CorsComponent
  //       weId={`${props.weId || ''}_mq4b2m`}
  //       app="@weapp/fnabstravel"
  //       compName="TripTravelOrderComp"
  //       visible
  //       needOpenPriceCompare={false}
  //       client="MOBILE"
  //       pageType="ORDER"
  //       onClose={() => setMockVisible(false)}
  //     />
  //   );
  // };
  // 列表区
  const getColumns = () => {
    const _commonCls = `${cls}-section3-list-table-common`;
    const _cls = getChannel() === 'bizFlight' ? `${cls}-section3-list-table-airItem` : `${cls}-section3-list-table-hotelItem`;
    const commonColumns: ITableColumn[] = [
      {
        title: '',
        dataIndex: 'cheap',
        width: '24%',
        bodyRender: (data: any) => {
          const { lowestInfo, infoList } = data;
          // 以infoList为准
          const lowerItem = infoList.find((item: any) => item.platform === lowestInfo?.platform);
          if (!lowerItem) {
            return '';
          }
          const { price, platform } = lowerItem;
          const providerItem = getProviderById(platform);
          if (!providerItem) {
            return '';
          }
          return (
            <div className={`${_commonCls}-cheap`}>
              <div className={`${_commonCls}-cheap-num`}>
                <div>￥</div>
                <div>{price}</div>
              </div>
              <div className={`${_commonCls}-cheap-company`}>
                <Icon
                  weId={`${props.weId || ''}_ukkbvt`}
                  name={providerItem.icon}
                  className={`${_commonCls}-cheap-company-icon`}
                  size="md"
                />
                <div>{providerItem.content}</div>
              </div>
            </div>
          );
        },
      },
      {
        title: '',
        dataIndex: 'order',
        width: '26%',
        bodyRender: (data: any) => {
          const { lowestInfo, infoList } = data;
          if (!lowestInfo || !infoList || !infoList.length) return '';
          // * 以infoList为准
          const lowerItem = infoList.find((item: any) => item.platform === lowestInfo.platform);
          if (!lowerItem) {
            return '';
          }
          const orderList = infoList.filter((item: any) => item.platform !== lowestInfo.platform);
          return (
            <div className={`${_commonCls}-order`}>
              <div className={`${_commonCls}-order-btn`}>
                <div className={`${_commonCls}-order-btn-v2`} onClick={() => order(lowerItem)}>
                  {getLabel('268023', '订')}
                </div>
              </div>
              {orderList.length ? (
                <div className={`${_commonCls}-order-list`}>
                  {orderList.map((item: any) => {
                    const providerItem = getProviderById(item.platform);
                    return (
                      <div className={`${_commonCls}-order-list-item`} key={item.platform} onClick={() => order(item)}>
                        <div>
                          <div>{getLabel('268023', '订')}</div>
                          <div title={providerItem?.content || ''}>{providerItem?.content || ''}</div>
                        </div>
                        <div>￥{item.price}</div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                ''
              )}
            </div>
          );
        },
      },
    ];
    const columns: ITableColumn[] = [
      {
        title: '',
        dataIndex: 'airplaneName',
        isPrimaryKey: true,
        width: '20%',
        fixed: true,
        bodyRender: (data: any) => {
          const { lowestInfo } = data;
          if (!lowestInfo) return '';
          const { logoSrc, airplaneName, departFlightNumber } = lowestInfo;
          return (
            <div className={`${_cls}-name`}>
              <div className={`${_cls}-name-icon`}>
                <img src={logoSrc} alt={airplaneName} />
              </div>
              <div className={`${_cls}-name-info`}>
                <div>{airplaneName}</div>
                <div>{departFlightNumber}</div>
              </div>
            </div>
          );
        },
      },
      {
        title: '',
        dataIndex: 'info',
        width: '30%',
        bodyRender: (data: any) => {
          const { lowestInfo } = data;
          if (!lowestInfo) {
            return '';
          }
          const { departTime, departAirportName, departTerminalName, arriveTime, arriveAirportName, arriveTerminalName, timeSpanMillis } =
            lowestInfo;
          return (
            <div className={`${_cls}-date`}>
              <div className={`${_cls}-date-item`}>
                <div>{departTime}</div>
                <div>{`${departAirportName}${departTerminalName}`}</div>
              </div>
              <div className={`${_cls}-date-time`}>
                <div className={`${_cls}-date-time-icon`}>
                  <div className={`${_cls}-date-time-icon-line line1`} />
                  {/* @ts-ignore  */}
                  <Icon weId={`${props.weId || ''}_22bwyz`} name="Icon-custom43-02-o" size="md" />
                  <div className={`${_cls}-date-time-icon-line line2`} />
                </div>
                <div className={`${_cls}-date-time-text`}>{formatDate(timeSpanMillis)}</div>
              </div>
              <div className={`${_cls}-date-item`}>
                <div>{arriveTime}</div>
                <div className={`${_cls}-date-item-to`}>
                  {arriveAirportName}
                  {arriveTerminalName}
                </div>
              </div>
            </div>
          );
        },
      },
      ...commonColumns,
    ];
    const hColumns: ITableColumn[] = [
      {
        title: '',
        dataIndex: 'info',
        isPrimaryKey: true,
        width: '50%',
        fixed: true,
        bodyRender: (data: any) => {
          const { lowestInfo } = data;
          if (!lowestInfo) return '';
          const { hotelName, hotelAddress, hotelImg, tags = '' } = lowestInfo;
          const tagsList = JSON.parse(tags);
          return (
            <div className={`${_cls}-info`}>
              <div className={`${_cls}-info-banner`} onClick={() => setPreviewUrl(hotelImg)}>
                <LazyLoadImg weId={`${props.weId || ''}_h1fg7r`} src={hotelImg} alt={hotelName} width={360} height={200} />
              </div>
              <div className={`${_cls}-info-collect`}>
                <div>
                  <Popover weId={`${props.weId || ''}_8r46pp`} popup={<span>{hotelName}</span>} placement="top" popoverType="tooltip">
                    <div>{hotelName}</div>
                  </Popover>
                  <div title={hotelAddress}>{hotelAddress}</div>
                </div>
                <div>
                  {tagsList.map((i: any, index: number) => {
                    return <div key={index}>{i}</div>;
                  })}
                </div>
              </div>
            </div>
          );
        },
      },
      ...commonColumns,
    ];
    if (getChannel() === 'bizFlight') {
      return columns;
    }
    return hColumns;
  };
  const _getPageInfo = () => {
    const { current, pageSize, paginationType } = getPageInfo();
    return {
      paginationType: paginationType as ITablePaginationType,
      value: current,
      pageSize: pageSize,
      total: getListData().length,
      showJumper: true,
      onChange: (page: number, size: number) => {
        setPageInfo({ ...pageInfo, current: page, pageSize: size });
      },
    };
  };
  const renderList = () => {
    const _list = getListData();
    if (!pending && isEmpty(_list)) {
      return (
        <Empty
          weId={`${props.weId || ''}_y2h798`}
          title={errText || `${getLabel('41515', '暂无数据')}`}
          image={
            <Icon
              weId={`${props.weId || ''}_8a7l9p`}
              style={{ width: 160, height: 160 }}
              // @ts-ignore
              name={'Icon-empty-drafts-mcolor'}
            />
          }
        />
      );
    }
    return (
      <Table
        weId={`${props.weId || ''}_rbw2do`}
        columns={getColumns()}
        data={getListData()}
        loading={pending}
        isShowHead={false}
        pageInfo={_getPageInfo()}
        className={`${cls}-section3-list-table ${getChannel() === 'bizFlight' ? 'bizFlight' : 'bizHotel'}`}
        scroll={{ x: 1100 }}
      />
    );
  };
  const renderSection3 = () => {
    if (pending) {
      return (
        <div className={`${cls}-commonSection ${cls}-section3-loading`}>
          <div className={`${cls}-section3-loading-ct`}>
            <Spin weId={`${props.weId || ''}_wmwokh`} spinning />
            <div className={`${cls}-section3-loading-ct-text`}>{getLabel('269033', '正在加载数据，请稍后...')}</div>
          </div>
        </div>
      );
    }
    const getBtnConfig = () => {
      if (getChannel() === 'bizFlight') {
        return [
          {
            id: 'price',
            content: getLabel('268024', '价格排序'),
          },
          {
            id: 'timeSpanMillis',
            content: getLabel('268025', '时间排序'),
          },
        ];
      }
      return [
        {
          id: 'normal',
          content: getLabel('268053', '综合排序'),
        },
        {
          id: 'price',
          content: getLabel('268024', '价格排序'),
        },
      ];
    };
    const orderInfo = getChannel() === 'bizFlight' ? airOrder : hotelOrder;
    return (
      <div className={`${cls}-commonSection ${cls}-section3`}>
        <div className={`${cls}-section3-tab`}>
          <div className={`${cls}-section3-tab-left`}>
            {getBtnConfig().map(item => (
              <div
                className={`${cls}-section3-tab-btn ${orderInfo.sortKey === item.id ? `active` : ''}`}
                key={item.id}
                onClick={() => changeOrderInfo('sortKey', item.id)}
              >
                {item.content}
                {orderInfo.sortKey === item.id && orderInfo.sortKey !== 'normal' && (
                  <Icon weId={`${props.weId || ''}_ysm524`} name={orderInfo.sortWay === 'asce' ? 'Icon-Down-arrow05' : 'Icon-up-arrow05'} />
                )}
              </div>
            ))}
          </div>
          {lastTime && (
            <div className={`${cls}-section3-tab-right`}>
              {`${getLabel('270082', '更新于')}：${lastTime}`}
              {/* @ts-ignore */}
              <Icon weId={`${props.weId || ''}_22bwyz`} name="Icon-Refresh02" onClick={() => getData(true)} />
            </div>
          )}
        </div>
        <div className={`${cls}-section3-list`}>{renderList()}</div>
      </div>
    );
  };
  return (
    <div className={cls}>
      <header className={`${cls}-header ${isScroll ? `${cls}-header-scroll` : ''}`}>
        <div className={`${cls}-header-icon`}>
          {/* @ts-ignore */}
          <Icon weId={`${props.weId || ''}_tipjgj`} name="Icon-Business-Travel-Integration" size="sm" />
        </div>
        <div className={`${cls}-header-title`}>{getLabel('268026', '商旅比价')}</div>
      </header>
      <div className={`${cls}-content`}>
        <TransitionEle weId={`${props.weId || ''}_2mtup3`} updateKey={getChannel()}>
          {getChannel() === 'bizFlight' ? (
            <div className={`${cls}-content-bgIcon ${cls}-content-airIcon`} />
          ) : (
            <div className={`${cls}-content-bgIcon ${cls}-content-hotelIcon`} />
          )}
        </TransitionEle>
        <div className={`${cls}-section1`}>
          <div className={`${cls}-section1-line1`}>
            <span>{getLabel('268026', '商旅比价')} · </span>
            <span>
              <TransitionEle weId={`${props.weId || ''}_qvn7jx`} updateKey={getChannel()}>
                {getChannel() === 'bizFlight' ? <span>{getLabel('268002', '订机票')}</span> : <span>{getLabel('268003', '找酒店')}</span>}
              </TransitionEle>
            </span>
          </div>
          <div className={`${cls}-section1-line2`}>
            <TransitionEle weId={`${props.weId || ''}_7ct84g`} updateKey={getChannel()}>
              {getChannel() === 'bizFlight' ? (
                <span>Compare prices for business trips and book air tickets</span>
              ) : (
                <span>Compare prices for business trips · Find hotels</span>
              )}
            </TransitionEle>
          </div>
        </div>
        {renderSection2()}
        {renderSection3()}
        {/* {mockVisible && mockRender()} */}
        {previewUrl && <Lightbox weId={`${props.weId || ''}_e6rs14`} mainSrc={previewUrl} onCloseRequest={() => setPreviewUrl('')} />}
      </div>
    </div>
  );
};

export default PriceComparison;
