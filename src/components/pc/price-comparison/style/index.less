@import '../../../../style/prefix.less';

.@{weappFnaBsTravelClsPrefix}-priceComparison {
    width: 100%;
    height: 100%;
    background: url('../../../../assets/price-comparison/bg.jpg');
    background-size: cover;
    padding-top: 50px;
    overflow-y: scroll;
    overflow-x: hidden;
    &-header {
        height: 50px;
        display: flex;
        align-items: center;
        padding: 0 16px;
        width: 100%;
        position: fixed;
        left: 0;
        top: 0;
        transition: all .3s;
        &-scroll{
            background: aliceblue;
            box-shadow: rgba(0, 0, 0, 0.06) 0px 4px 8px 0px;
            z-index: 999;
        }
        &-icon{
            border: 0.7px solid rgba(104,165,255,1);
            border-radius: 2px;
            padding: 5px;
            .ui-icon-svg{
                color: var(--primary);
            }
        }
        &-title{
            color: #111;
            font-size: var(--font-size-14);
            margin-left: 10px;
            font-weight: bold;
        }
    }
    &-content{
        font-size: var(--font-size-14);
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
        width: 100%;
        &-bgIcon{
            position: absolute;
            top: 0;
            z-index: 1;
            right: -110px;
            transition: all .3s;
        }
        &-airIcon{
            width: 551px;
            height: 180px;
            background: url('../../../../assets/price-comparison/airplane.png');
            background-size: cover;
            top: 40px;
        }
        &-hotelIcon{
            background: url('../../../../assets/price-comparison/hotel.png');
            height: 288px;
            width: 736px;
            top: -40px;
        }
    }
    &-commonSection{
        background-image: linear-gradient(270deg, #FFFFFF 0%, rgba(255,255,255,0.40) 100%);
        border-radius: 10px;
        margin-bottom: 16px;
        z-index: 2;
        position: relative;
    }

    &-section1{
        padding: 47px 60px;
        &-line1{
            font-size: 34px;
            font-weight: bold;
            span{
                &:first-child{
                    color: var(--main-fc);
                }
                &:last-child{
                    color: var(--primary);
                }
            }
        }
        &-line2{
            margin-top: 18px;
            font-size: var(--font-size-16);
            color: #A5A9BD;
        }
    }
    &-section2{
        padding: 24px 0;
        &-line1{
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            &-item{
                font-size: var(--font-size-16);
                font-weight: bold;
                color: #444444;
                transition: all .3s;
                position: relative;
                // cursor: pointer;
                &:last-child{
                    margin-left: 28px;
                }
                // &:hover{
                //     color: var(--primary);
                // }
                &-active{
                    color: var(--primary);
                    &::after{
                        position: absolute;
                        left: 10px;
                        top: 10px;
                        content: '';
                        opacity: 0.2;
                        background: var(--primary);
                        width: 90%;
                        height: 10px;
                        transform: skewX(-45deg);
                    }
                }
            }
        }
        &-line2{
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 66px;
            &-item{
                width: 300px;
                height: 62px;
                background: rgba(220,221,232,0.16);
                border: 1px solid rgba(220,221,232,1);
                padding: 12px 16px;
                border-radius: 6px;
                &.mgl16{
                    margin-left: 16px;
                }
                &-md{
                    width: 225px;
                }
                &-sm{
                    width: 200px;
                }
                &-ssm{
                    width: 180px;
                }
                &-label{
                    font-size: var(--font-size-12);
                    color: #666666;
                    margin-bottom: 6px;
                }
                &-change{
                    width: 38px;
                    height: 38px;
                    opacity: 0.6;
                    background: #DCDDE8;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 20px;
                    border-radius: 50%;
                    transition: all .3s;
                    &.isReturn{
                        cursor: pointer;
                    }
                    &.hasReturn{
                        transform: rotate(-90deg)
                    }
                }
                &-divide{
                    opacity: 0.7;
                    background: #DCDDE8;
                    width: 1px;
                    height: 30px;
                    margin: 0 44px;
                    &.mgl34{
                        margin: 0 34px;
                    }
                    &.mgl24{
                        margin: 0 24px;
                    }
                }
                &-hDivide{
                    width: 16px;
                    height: 2px;
                    opacity: 0.7;
                    background: #DCDDE8;
                    margin: 0 10px;
                }
                &-main{
                    font-size: var(--font-size-16);
                    color: var(--main-fc);
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    .ui-input-wrap, .ui-input, .ui-date-picker, .ui-date-picker-wrap, .ui-date-picker-rangeWrap, .ui-input-number{
                        &::placeholder{
                            font-size: var(--font-size-16);
                        }
                    }
                    &-input, .ui-input-wrap, .ui-input, .ui-date-picker, .ui-date-picker-wrap, .ui-date-picker-rangeWrap{
                        border: none;
                        background-color: transparent;
                        min-height: 22px;
                        position: relative;
                        transition: all .3s;
                        font-size: 16px;
                        &.is-focus{
                            box-shadow: none;
                        }
                    }
                    &.needAnimate{
                        .ui-input-wrap, .ui-input, .ui-date-picker, .ui-date-picker-wrap, .ui-date-picker-rangeWrap{
                            padding: 0 8px;
                            left: -8px;
                        }
                    }
                    .ui-date-picker-maxWidth{
                        width: calc(100% + 50px);
                        max-width: inherit;
                    }
                    .ui-date-picker-wrap, .ui-date-picker, .ui-input-number{
                        height: 22px;
                        line-height: 22px;
                        padding: 0;
                        left: 0;
                        .ui-input-wrap, .ui-input{
                            left: 0;
                            padding: 0;
                        }
                    }
                    .ui-input-number{
                        max-width: 60px;
                        &:first-child{
                            text-align: left;
                        }
                        &:last-child{
                            text-align: right;
                        }
                    }
                }
            }
        }
    }
    &-section3{
        min-height: 300px;
        padding: 16px 24px;
        &-loading{
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            &-ct{
                display: flex;
                align-items: center;
                &-text{
                    margin-left: 12px;
                    color: var(--secondary-fc);
                }
            }
        }
        &-tab{
            height: 30px;
            display: flex;
            position: relative;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            &-left{
                display: flex;
                overflow: hidden;
                background: #fff;
                border-radius: 3px;
                padding: 2px;
            }
            &-right{
                color: var(--secondary-fc);
                font-size: var(--font-size-14);
                display: flex;
                align-items: center;
                .ui-icon {
                    margin-left: 10px;
                    cursor: pointer;
                    transition: all .3s;
                    &:hover{
                        color: var(--primary);
                    }
                }
            }
            &-btn{
                border-radius: 3px;
                cursor: pointer;
                transition: all .3s;
                font-size: var(--font-size-12);
                text-align: center;
                line-height: 26px;
                color: #666;
                padding: 0 12px;
                .ui-icon{
                    position: relative;
                    top: -2px;
                    margin-left: 4px;
                }
                &:hover:not(&.active){
                    padding: 0 16px;
                }
                &.active {
                    background: var(--primary);
                    color: #fff;
                }
            }
        }
        &-list{
            .ui-table-grid{
                background-color: transparent;
                border: none;
            }
            .ui-table-grid-td{
                padding: 0;
            }
            .ui-table-grid-tr:hover, .ui-table-grid-tr__mouseover, .ui-table-grid-tr:hover td, .ui-table-grid-tr__mouseover td, .ui-table-grid-td.ui-table-grid-td-sticky{
                background-color: transparent;
            }
            .ui-table-grid-td, .ui-table-grid-th{
                border-bottom: 1px dashed rgba(220,221,232,1);
            }
            .ui-table-grid.ui-table-grid-scroll-position-middle .ui-table-grid-td-sticky-left, .ui-table-grid.ui-table-grid-scroll-position-right .ui-table-grid-td-sticky-left{
                background: aliceblue;
            }
            .ui-table-grid.ui-table-grid-scroll-position-left.ui-table-grid-scroll-position-right{
                .ui-table-grid-td.ui-table-grid-td-sticky{
                    background: transparent;
                }
            }
            .ui-empty-title{
                font-size: var(--font-size-14);
            }
            &-table{
                &.bizFlight{
                    .ui-table-grid-td, .ui-table-grid-th{
                        height: 100px;
                        line-height: 100px;
                    }
                }
                &.bizHotel {
                    .ui-table-grid-td, .ui-table-grid-th{
                        height: 140px;
                        line-height: 140px;
                    }
                }
                &-common {
                    &-cheap{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        &-num{
                            display: flex;
                            align-items: flex-end;
                            color: #FF4D4F;
                            font-weight: bold;
                            height: 28px;
                            line-height: 28px;
                            & > div:first-child{
                                font-size: 14px;
                                line-height: 20px;
                            }
                            & > div:last-child{
                                font-size: 28px;
                            }
                        }
                        &-company{
                            color: var(--main-fc);
                            font-size: var(--font-size-12);
                            display: flex;
                            align-items: center;
                            margin-left: 16px;
                            &-icon{
                                width: 18px;
                                height: 18px;
                                object-fit: cover;
                                margin-right: 6px;
                            }
                        }
                    }
                    &-order{
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        height: 100%;
                        &-btn{
                            display: flex;
                            justify-content: flex-end;
                            margin-bottom: 10px;
                            &-v2{
                                border-radius: 3px;
                                background: var(--primary);
                                height: 30px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: #fff;
                                cursor: pointer;
                                padding: 0 9px;
                            }
                        }
                        &-list{
                            display: flex;
                            align-items: center;
                            justify-content: flex-end;
                            flex-wrap: wrap;
                            margin: 0 -10px 0;
                            &-item{
                                display: flex;
                                align-items: center;
                                cursor: pointer;
                                line-height: 12px;
                                height: 12px;
                                margin: 0 10px 10px 0;
                                & > div {
                                    transition: all .3s;
                                    display: inline-block;
                                    word-break: break-word;
                                }
                                &:hover{
                                    color: var(--primary);
                                    & > div:first-child {
                                        & > div {
                                            &:first-child{
                                                background: var(--primary);
                                            }
                                        }
                                    }
                                    & > div:nth-child(2) {
                                        &::after{
                                            background-color: var(--primary);
                                        }
                                    }
                                }
                                &:last-child{
                                    // margin-right: 0;
                                    // margin-bottom: 0;
                                }
                                & > div:first-child{
                                    // margin-right: 16px;
                                    position: relative;
                                    display: flex;
                                    align-items: center;
                                    &::after{
                                        position: absolute;
                                        top: 8px;
                                        right: -9px;
                                        content: '';
                                        width: 2px;
                                        height: 2px;
                                        border-radius: 50%;
                                        background-color: var(--regular-fc);
                                    }
                                    & > div {
                                        &:first-child{
                                            background: #C1C1C1;
                                            border-radius: 3px;
                                            padding: 3px;
                                            font-size: 10px;
                                            color: #fff;
                                            font-weight: bold;
                                            margin-right: 8px;
                                        }
                                        &:nth-child(2){
                                            max-width: 48px;
                                            white-space: nowrap;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                &-airItem{
                    &-name{
                        display: flex;
                        align-items: center;
                        &-icon{
                            max-width: 42px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            height: 34px;
                            img{
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }
                        }
                        &-info{
                            margin-left: 10px;
                            line-height: 14px;
                            & > div{
                                &:first-child{
                                    margin-bottom: 8px;
                                    font-size: var(--font-size-14);
                                    color: var(--main-fc);
                                }
                                &:last-child{
                                    font-size: var(--font-size-12);
                                    color: var(--secondary-fc);
                                }
                            }
                        }
                    }
                    &-date{
                        display: flex;
                        align-items: center;
                        flex-direction: row;
                        padding: 0 20px;
                        &-item{
                            &:first-child, &:last-child{
                                width: 100px;
                                flex-shrink: 0;
                            }
                            &:first-child{
                                text-align: left;
                            }
                            &:last-child{
                                text-align: right;
                            }
                            & > div {
                                color: var(--main-fc);
                                &:first-child {
                                    font-size: 24px;
                                    font-weight: bold;
                                    margin-bottom: 7px;
                                    height: 24px;
                                    line-height: 24px;
                                }
                                &:last-child {
                                    font-size: var(--font-size-12);
                                    line-height: 12px;
                                    word-wrap: break-word;
                                    word-break: break-word;
                                    overflow: hidden;
                                    white-space: wrap;
                                }
                            }
                            &-to{
                                display: flex;
                                justify-content: flex-end;
                            }
                        }
                        &-time{
                            line-height: initial;
                            &-icon{
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-bottom: 4px;
                                &-line{
                                    transform: scaleX(-1);
                                    border-radius: 1px;
                                    width: 34px;
                                    height: 2px;
                                    &.line1{
                                        background-image: linear-gradient(90deg, rgba(162,189,223,0.80) 0%, rgba(162,189,223,0.00) 100%);
                                    }
                                    &.line2{
                                        background-image: linear-gradient(90deg, rgba(162,189,223,0.00) 0%, rgba(162,189,223,0.80) 100%);
                                    }
                                }
                                .ui-icon{
                                    color: #A2BDDF;
                                    margin: 0 6px;
                                }
                            }
                            &-text{
                                font-size: 12px;
                                color: var(--secondary-fc);
                                text-align: center;
                            }
                        }
                    }
                }
                &-hotelItem{
                    &-info{
                        display: flex;
                        align-items: center;
                        height: 100px;
                        &-banner{
                            width: 180px;
                            height: 100px;
                            flex-shrink: 0;
                            border-radius: 6px;
                            overflow: hidden;
                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }
                        }
                        &-collect{
                            line-height: initial;
                            flex: 1;
                            overflow: hidden;
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                            height: 100%;
                            & > div{
                                padding-left: 16px;
                                width: calc(100% - 16px);
                            }
                            & > div:first-child{
                                & > div:first-child{
                                    color: var(--main-fc);
                                    font-size: var(--font-size-18);
                                    font-weight: bold;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                    cursor: pointer;
                                    margin-bottom: 6px;
                                    display: inline-block;
                                    max-width: 100%;
                                }
                                & > div:last-child{
                                    color: var(--secondary-fc);
                                    font-size: var(--font-size-12);
                                    white-space: normal;
                                    display: -webkit-box;
                                    -webkit-box-orient: vertical;
                                    -webkit-line-clamp: 2; /* 限制为两行 */
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                }
                            }
                            & > div:last-child{
                                display: flex;
                                & > div {
                                    padding: 2px 8px;
                                    color: var(--primary);
                                    margin-right: 4px;
                                    position: relative;
                                    &:last-child{
                                        margin-right: 0;
                                    }
                                    &::before{
                                        position: absolute;
                                        content: '';
                                        top: 0;
                                        left: 0;
                                        width: 100%;
                                        height: 100%;
                                        background: var(--primary);
                                        opacity: 0.15;
                                        border-radius: 3px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .fade-in {
        opacity: 1;
        transition: opacity 1s ease-in-out;
    }

    .fade-out {
        opacity: 0;
        transition: opacity 1s ease-in-out;
    }
}


@media screen and (max-width: 900px) {
    .@{weappFnaBsTravelClsPrefix}-priceComparison {
        &-content-bgIcon {
            display: none;
        }
    }
}

@media screen and (max-width: 720px) {
    .@{weappFnaBsTravelClsPrefix}-priceComparison {
        &-section2 {
            height: auto;
            &.bizFlight{
                .weapp-fna-bstravel-priceComparison-section2-line2-item{
                    width: 40%;
                }
            }
            &.bizHotel{
                .weapp-fna-bstravel-priceComparison-section2-line2-item{
                    width: 48%;
                }
            }
            &-line2 {
                flex-wrap: wrap;
                justify-content: space-between;
                &-item{
                    margin-bottom: 16px;
                    &-divide{
                        display: none;
                    }
                }
            }
        }
    }
}
@media screen and (max-width: 550px) {
    .@{weappFnaBsTravelClsPrefix}-priceComparison {
        &-section2 {
            height: auto;
            &.bizFlight{
                .weapp-fna-bstravel-priceComparison-section2-line2-item{
                    width: 36%;
                    &.departDate{
                        width: 100%;
                        &-main{
                            font-size: var(--font-size-14) !important;
                        }
                    }
                    &-main{
                        font-size: var(--font-size-12);
                    }

                    &-change{
                        position: relative;
                        top: -8px;
                        margin: 0 10px;
                    }
                }
            }
            &.bizHotel{
                .weapp-fna-bstravel-priceComparison-section2-line2-item{
                    width: 100%;
                    &.mgl16{
                        margin-left: 0
                    }
                }
            }
        }
    }
}