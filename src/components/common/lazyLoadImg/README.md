# LazyLoadImage 懒加载图片组件

一个基于 Intersection Observer API 的真正懒加载图片组件，支持图片压缩、重试机制和错误处理。

## 特性

- ✅ **真正的懒加载**: 只在图片即将进入视口时才开始加载
- ✅ **图片压缩**: 可选的 Canvas 图片压缩功能
- ✅ **重试机制**: 加载失败时自动重试，防止死循环
- ✅ **错误处理**: 完善的错误处理和状态显示
- ✅ **占位符支持**: 支持自定义占位图
- ✅ **TypeScript**: 完整的 TypeScript 类型支持
- ✅ **性能优化**: 防抖、内存清理等性能优化

## 基本用法

```tsx
import LazyLoadImage from '../../common/lazyLoadImg';

// 基本使用
<LazyLoadImage
  src="https://example.com/image.jpg"
  alt="示例图片"
  width={300}
  height={200}
/>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `src` | `string` | - | **必需**，图片 URL |
| `alt` | `string` | `''` | 图片 alt 属性 |
| `width` | `number` | - | 图片宽度 |
| `height` | `number` | - | 图片高度 |
| `className` | `string` | `''` | CSS 类名 |
| `style` | `React.CSSProperties` | `{}` | 内联样式 |
| `defaultImgSrc` | `string` | - | 占位图 URL |
| `rootMargin` | `string` | `'50px'` | Intersection Observer 的 rootMargin |
| `threshold` | `number` | `0.1` | Intersection Observer 的 threshold |
| `enableCompression` | `boolean` | `true` | 是否启用图片压缩 |
| `compressionQuality` | `number` | `0.8` | 压缩质量 (0-1) |
| `maxRetries` | `number` | `2` | 最大重试次数 |
| `retryDelay` | `number` | `1000` | 重试延迟时间（毫秒） |
| `onLoad` | `() => void` | - | 图片加载成功回调 |
| `onError` | `(error: Error) => void` | - | 图片加载失败回调 |

## 使用示例

### 1. 基本懒加载

```tsx
<LazyLoadImage
  src="https://example.com/image.jpg"
  alt="基本示例"
  width={300}
  height={200}
/>
```

### 2. 带占位图

```tsx
<LazyLoadImage
  src="https://example.com/image.jpg"
  defaultImgSrc="/placeholder.jpg"
  alt="带占位图"
  width={300}
  height={200}
/>
```

### 3. 禁用压缩

```tsx
<LazyLoadImage
  src="https://example.com/image.jpg"
  alt="禁用压缩"
  width={300}
  height={200}
  enableCompression={false}
/>
```

### 4. 自定义重试配置

```tsx
<LazyLoadImage
  src="https://example.com/image.jpg"
  alt="自定义重试"
  width={300}
  height={200}
  maxRetries={3}
  retryDelay={2000}
  onError={(error) => console.error('加载失败:', error)}
/>
```

### 5. 提前加载（更大的 rootMargin）

```tsx
<LazyLoadImage
  src="https://example.com/image.jpg"
  alt="提前加载"
  width={300}
  height={200}
  rootMargin="200px"
/>
```

## 状态说明

组件会根据不同状态显示不同内容：

1. **未进入视口**: 显示占位符或空白
2. **加载中**: 显示 "Loading..." 文本
3. **加载成功**: 显示压缩后的图片
4. **加载失败**: 显示 "load failed" 文本

## 注意事项

1. **防止死循环**: 组件内置了重试机制和失败记录，防止无限重试
2. **内存管理**: 组件会自动清理 Intersection Observer 和相关资源
3. **兼容性**: 需要浏览器支持 Intersection Observer API
4. **图片压缩**: 压缩功能需要浏览器支持 Canvas API

## 迁移指南

从旧版本迁移时，只需要替换组件导入，API 基本兼容：

```tsx
// 旧版本
import LazyLoadImg from '../../common/lazyLoadImg';

// 新版本（API 兼容）
import LazyLoadImage from '../../common/lazyLoadImg';
```
