import React from 'react';
import LazyLoadImage from './index';

// 测试组件
const LazyLoadImageTest: React.FC = () => {
  const testImages = [
    'https://picsum.photos/300/200?random=1',
    'https://picsum.photos/300/200?random=2',
    'https://picsum.photos/300/200?random=3',
    'https://invalid-url.jpg', // 测试错误处理
    'https://picsum.photos/300/200?random=4',
    'https://picsum.photos/300/200?random=5',
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h2>懒加载图片测试</h2>
      <p>向下滚动查看懒加载效果</p>
      
      {/* 添加一些空间，确保图片不在初始视口内 */}
      <div style={{ height: '100vh', backgroundColor: '#f0f0f0', marginBottom: '20px' }}>
        <p style={{ paddingTop: '50vh', textAlign: 'center' }}>向下滚动查看图片</p>
      </div>

      {testImages.map((src, index) => (
        <div key={index} style={{ marginBottom: '50px' }}>
          <h3>图片 {index + 1}</h3>
          <LazyLoadImage
            src={src}
            alt={`测试图片 ${index + 1}`}
            width={300}
            height={200}
            style={{ border: '1px solid #ddd' }}
            onLoad={() => console.log(`图片 ${index + 1} 加载成功`)}
            onError={(error) => console.error(`图片 ${index + 1} 加载失败:`, error)}
          />
        </div>
      ))}

      {/* 测试不同配置 */}
      <div style={{ marginBottom: '50px' }}>
        <h3>禁用压缩的图片</h3>
        <LazyLoadImage
          src="https://picsum.photos/300/200?random=6"
          alt="禁用压缩测试"
          width={300}
          height={200}
          enableCompression={false}
          style={{ border: '1px solid #ddd' }}
        />
      </div>

      <div style={{ marginBottom: '50px' }}>
        <h3>带占位图的图片</h3>
        <LazyLoadImage
          src="https://picsum.photos/300/200?random=7"
          defaultImgSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWNoOS9jeWbvjwvdGV4dD48L3N2Zz4="
          alt="带占位图测试"
          width={300}
          height={200}
          style={{ border: '1px solid #ddd' }}
        />
      </div>

      <div style={{ marginBottom: '50px' }}>
        <h3>自定义 rootMargin 的图片</h3>
        <LazyLoadImage
          src="https://picsum.photos/300/200?random=8"
          alt="自定义 rootMargin 测试"
          width={300}
          height={200}
          rootMargin="200px"
          style={{ border: '1px solid #ddd' }}
        />
      </div>
    </div>
  );
};

export default LazyLoadImageTest;
