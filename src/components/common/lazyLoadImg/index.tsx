import React, { useState, useEffect, useCallback, useRef } from 'react';

interface ILazyLoadImage {
  src: string; // 真实图片
  defaultImgSrc?: string; // 占位图
  width?: number;
  height?: number;
  alt?: string;
  className?: string;
  style?: React.CSSProperties;
  rootMargin?: string; // Intersection Observer 的 rootMargin，默认 '50px'
  threshold?: number; // Intersection Observer 的 threshold，默认 0.1
  enableCompression?: boolean; // 是否启用图片压缩，默认 true
  compressionQuality?: number; // 压缩质量，默认 0.8
  maxRetries?: number; // 最大重试次数，默认 2
  retryDelay?: number; // 重试延迟时间（毫秒），默认 1000
  onLoad?: () => void; // 图片加载完成回调
  onError?: (error: Error) => void; // 图片加载失败回调
  [key: string]: any;
}

export default function LazyLoadImage(props: ILazyLoadImage) {
  const {
    src,
    defaultImgSrc = '',
    width,
    height,
    alt = '',
    className = '',
    style = {},
    rootMargin = '50px',
    threshold = 0.1,
    enableCompression = true,
    compressionQuality = 0.8,
    maxRetries = 2,
    retryDelay = 1000,
    onLoad,
    onError,
    ...restProps
  } = props;

  const [isIntersecting, setIsIntersecting] = useState(false);
  const [imgLoaded, setImgLoaded] = useState(false);
  const [imgError, setImgError] = useState(false);
  const [compressedImg, setCompressedImg] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const imgRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const retryCountRef = useRef<number>(0);
  const loadedUrlsRef = useRef<Set<string>>(new Set()); // 记录已经尝试加载过的URL

  // 压缩图片
  const compressImage = useCallback(
    (imageUrl: string, originalImg: HTMLImageElement) => {
      if (!enableCompression || !canvasRef.current) {
        return imageUrl;
      }

      try {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (!ctx) return imageUrl;

        const _width = width || originalImg.width;
        const _height = height || originalImg.height;

        canvas.width = _width;
        canvas.height = _height;

        ctx.drawImage(originalImg, 0, 0, _width, _height);
        return canvas.toDataURL('image/jpeg', compressionQuality);
      } catch (error) {
        console.warn('Image compression failed:', error);
        return imageUrl;
      }
    },
    [width, height, enableCompression, compressionQuality]
  );

  // 重置状态（当 src 改变时）
  const resetState = useCallback(() => {
    setImgLoaded(false);
    setImgError(false);
    setCompressedImg(null);
    setIsLoading(false);
    retryCountRef.current = 0;
  }, []);

  // 处理图片加载错误
  const handleImageError = useCallback((url: string, retryCount: number, error: Error) => {
    loadedUrlsRef.current.add(url);

    if (retryCount < maxRetries) {
      // 重试
      console.warn(`Image load failed, retrying (${retryCount + 1}/${maxRetries}):`, url);
      setTimeout(() => {
        // 直接调用加载逻辑，避免循环依赖
        if (!imgLoaded && !isLoading) {
          setIsLoading(true);
          setImgError(false);

          const img = new Image();
          img.crossOrigin = 'Anonymous';

          img.onload = () => {
            try {
              const finalImageSrc = compressImage(url, img);
              setCompressedImg(finalImageSrc);
              setImgLoaded(true);
              setIsLoading(false);
              retryCountRef.current = 0;
              loadedUrlsRef.current.delete(url);
              onLoad?.();
            } catch (error) {
              handleImageError(url, retryCount + 1, error as Error);
            }
          };

          img.onerror = () => {
            const error = new Error(`Failed to load image: ${url}`);
            handleImageError(url, retryCount + 1, error);
          };

          img.src = url;
        }
      }, retryDelay);
    } else {
      // 超过重试次数，标记为失败
      console.error(`Image load failed after ${maxRetries} retries:`, url);
      setImgError(true);
      setIsLoading(false);
      onError?.(error);
    }
  }, [maxRetries, retryDelay, onError, imgLoaded, isLoading, compressImage, onLoad]);

  // 加载图片（带重试机制）
  const loadImg = useCallback((url: string, retryCount = 0) => {
    // 防止重复加载和死循环
    if (!url || imgLoaded || isLoading) return;

    // 如果已经失败过且超过重试次数，直接返回
    if (loadedUrlsRef.current.has(url) && retryCount >= maxRetries) {
      setImgError(true);
      setIsLoading(false);
      onError?.(new Error(`Failed to load image after ${maxRetries} retries: ${url}`));
      return;
    }

    setIsLoading(true);
    setImgError(false);

    const img = new Image();
    img.crossOrigin = 'Anonymous';

    img.onload = () => {
      try {
        const finalImageSrc = compressImage(url, img);
        setCompressedImg(finalImageSrc);
        setImgLoaded(true);
        setIsLoading(false);
        retryCountRef.current = 0;
        // 成功加载后从失败记录中移除
        loadedUrlsRef.current.delete(url);
        onLoad?.();
      } catch (error) {
        console.error('Image processing failed:', error);
        handleImageError(url, retryCount, error as Error);
      }
    };

    img.onerror = () => {
      const error = new Error(`Failed to load image: ${url}`);
      handleImageError(url, retryCount, error);
    };

    img.src = url;
  }, [imgLoaded, isLoading, compressImage, onLoad, onError, maxRetries, handleImageError]);

  // 当 src 改变时重置状态
  useEffect(() => {
    resetState();
  }, [src, resetState]);

  // 设置 Intersection Observer
  useEffect(() => {
    if (!imgRef.current) return;

    const observer = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting && !isIntersecting) {
          setIsIntersecting(true);
        }
      },
      {
        rootMargin,
        threshold,
      }
    );

    observer.observe(imgRef.current);
    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [rootMargin, threshold, isIntersecting]);

  // 当图片进入视口时开始加载
  useEffect(() => {
    if (isIntersecting && src) {
      loadImg(src);
    }
  }, [isIntersecting, src, loadImg]);

  // 容器样式
  const containerStyle: React.CSSProperties = {
    display: 'inline-block',
    position: 'relative',
    width: width || 'auto',
    height: height || 'auto',
    backgroundColor: '#f5f5f5',
    ...style,
  };

  // 占位符样式
  const placeholderStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    color: '#999',
    fontSize: '12px',
  };

  // 加载中样式
  const loadingStyle: React.CSSProperties = {
    ...placeholderStyle,
    backgroundColor: '#fafafa',
  };

  return (
    <div ref={imgRef} className={className} style={containerStyle} {...restProps}>
      {/* 隐藏的 canvas 用于图片压缩 */}
      {enableCompression && <canvas ref={canvasRef} style={{ display: 'none' }} />}

      {/* 图片加载完成后显示 */}
      {imgLoaded && compressedImg && (
        <img
          src={compressedImg}
          alt={alt}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      )}

      {/* 加载中状态 */}
      {isLoading && !imgLoaded && <div style={loadingStyle}>Loading...</div>}

      {/* 错误状态或占位符 */}
      {!isLoading && !imgLoaded && (
        <div style={placeholderStyle}>
          {imgError ? (
            'load failed'
          ) : defaultImgSrc ? (
            <img
              src={defaultImgSrc}
              alt={alt}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
            />
          ) : (
            <div />
          )}
        </div>
      )}
    </div>
  );
}