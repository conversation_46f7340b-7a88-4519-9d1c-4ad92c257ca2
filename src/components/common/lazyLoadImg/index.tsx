import React, { useState, useEffect, useCallback } from 'react';
interface IProxyImage {
  src: string; // 真实图片
  defaultImgSrc?: string; // 占位图
  width?: number;
  height?: number;
  [key: string]: any;
}

export default function ProxyImage(props: IProxyImage) {
  const { src, defaultImgSrc = '', width, height, ...restProps } = props;
  const [imgLoaded, setImgLoaded] = useState(false);
  const [compressedImg, setCompressedImg] = useState(null as any);
  const loadImg = useCallback((url: string) => {
    const img = new Image();
    img.src = url;
    img.crossOrigin = 'Anonymous';
    img.onload = () => {
      const canvas: any = document.getElementById('canvas');
      const ctx = canvas.getContext('2d');
      const _width = width || img.width;
      const _height = height || img.height;
      canvas.width = _width;
      canvas.height = _height;
      ctx.drawImage(img, 0, 0, _width, _height);
      const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.8);
      setCompressedImg(compressedDataUrl);
      setImgLoaded(true);
      // fetch(compressedDataUrl)
      //   .then(res => res.blob())
      //   .then(blob => {
      //     console.log('Compressed Image Size:', blob.size, 'bytes');
      //   });
    };
  }, []);

  // 真实图片加载完成，显示真实图片
  useEffect(() => {
    loadImg(src);
  }, [src]);
  return (
    <div>
      {imgLoaded && <img src={compressedImg} {...restProps} />}
      <img id="originalImage" style={{ display: 'none' }} />
      <canvas id="canvas" style={{ display: 'none' }}></canvas>
    </div>
  );
}
