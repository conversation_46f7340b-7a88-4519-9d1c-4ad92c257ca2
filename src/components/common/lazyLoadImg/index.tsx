import React, { useState, useEffect, useCallback, useRef } from 'react';

interface ILazyLoadImage {
  src: string; // 真实图片
  defaultImgSrc?: string; // 占位图
  width?: number;
  height?: number;
  alt?: string;
  className?: string;
  style?: React.CSSProperties;
  rootMargin?: string; // Intersection Observer 的 rootMargin，默认 '50px'
  threshold?: number; // Intersection Observer 的 threshold，默认 0.1
  enableCompression?: boolean; // 是否启用图片压缩，默认 true
  compressionQuality?: number; // 压缩质量，默认 0.8
  onLoad?: () => void; // 图片加载完成回调
  onError?: () => void; // 图片加载失败回调
  [key: string]: any;
}

export default function LazyLoadImage(props: ILazyLoadImage) {
  const {
    src,
    defaultImgSrc = '',
    width,
    height,
    alt = '',
    className = '',
    style = {},
    rootMargin = '50px',
    threshold = 0.1,
    enableCompression = true,
    compressionQuality = 0.8,
    onLoad,
    onError,
    ...restProps
  } = props;

  const [isIntersecting, setIsIntersecting] = useState(false);
  const [imgLoaded, setImgLoaded] = useState(false);
  const [imgError, setImgError] = useState(false);
  const [compressedImg, setCompressedImg] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const imgRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // 压缩图片
  const compressImage = useCallback(
    (imageUrl: string, originalImg: HTMLImageElement) => {
      if (!enableCompression || !canvasRef.current) {
        return imageUrl;
      }

      try {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (!ctx) return imageUrl;

        const _width = width || originalImg.width;
        const _height = height || originalImg.height;

        canvas.width = _width;
        canvas.height = _height;

        ctx.drawImage(originalImg, 0, 0, _width, _height);
        return canvas.toDataURL('image/jpeg', compressionQuality);
      } catch (error) {
        console.warn('Image compression failed:', error);
        return imageUrl;
      }
    },
    [width, height, enableCompression, compressionQuality]
  );

  // 加载图片
  const loadImg = useCallback(
    (url: string) => {
      if (!url || imgLoaded || isLoading) return;

      setIsLoading(true);
      setImgError(false);

      const img = new Image();
      img.crossOrigin = 'Anonymous';

      img.onload = () => {
        try {
          const finalImageSrc = compressImage(url, img);
          setCompressedImg(finalImageSrc);
          setImgLoaded(true);
          setIsLoading(false);
          onLoad?.();
        } catch (error) {
          console.error('Image processing failed:', error);
          setImgError(true);
          setIsLoading(false);
          onError?.();
        }
      };

      img.onerror = () => {
        setImgError(true);
        setIsLoading(false);
        onError?.();
        debugger
      };

      img.src = url;
    },
    [imgLoaded, isLoading, compressImage, onLoad, onError]
  );

  // 设置 Intersection Observer
  useEffect(() => {
    if (!imgRef.current) return;

    const observer = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting && !isIntersecting) {
          setIsIntersecting(true);
        }
      },
      {
        rootMargin,
        threshold,
      }
    );

    observer.observe(imgRef.current);
    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [rootMargin, threshold, isIntersecting]);

  // 当图片进入视口时开始加载
  useEffect(() => {
    if (isIntersecting && src) {
      loadImg(src);
    }
  }, [isIntersecting, src, loadImg]);

  // 容器样式
  const containerStyle: React.CSSProperties = {
    display: 'inline-block',
    position: 'relative',
    width: width || 'auto',
    height: height || 'auto',
    backgroundColor: '#f5f5f5',
    ...style,
  };

  // 占位符样式
  const placeholderStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    color: '#999',
    fontSize: '12px',
  };

  // 加载中样式
  const loadingStyle: React.CSSProperties = {
    ...placeholderStyle,
    backgroundColor: '#fafafa',
  };

  return (
    <div ref={imgRef} className={className} style={containerStyle} {...restProps}>
      {/* 隐藏的 canvas 用于图片压缩 */}
      {enableCompression && <canvas ref={canvasRef} style={{ display: 'none' }} />}

      {/* 图片加载完成后显示 */}
      {imgLoaded && compressedImg && (
        <img
          src={compressedImg}
          alt={alt}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      )}

      {/* 加载中状态 */}
      {isLoading && !imgLoaded && <div style={loadingStyle}>Loading...</div>}

      {/* 错误状态或占位符 */}
      {!isLoading && !imgLoaded && (
        <div style={placeholderStyle}>
          {imgError ? (
            'load failed'
          ) : defaultImgSrc ? (
            <img
              src={defaultImgSrc}
              alt={alt}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
            />
          ) : (
            <div />
          )}
        </div>
      )}
    </div>
  );
}