/*
 * @desc: 通用动画过渡
 */
import React from 'react';
import { CSSTransition, SwitchTransition } from 'react-transition-group';
import { weappFnaBsTravelClsPrefix } from '../../../constants';
import './style/index.less'

interface IProps {
  children: React.ReactNode;
  weId?: string;
  cls?: string;
  updateKey: string;
}
const cls = `${weappFnaBsTravelClsPrefix}-common-transition`;

const TransitionEle: React.ComponentType<IProps> = props => {
  if (!props || !props.children || !props.updateKey) {
    return <div />;
  }
  return (
    <SwitchTransition weId={`${props.weId || ''}_8a39f7`} mode="out-in">
      <CSSTransition
        weId={`${props.weId || ''}_imzkzw`}
        classNames={`${props.cls || cls}`}
        timeout={300}
        unmountOnExit
        key={props.updateKey}
      >
        <span>{props.children}</span>
      </CSSTransition>
    </SwitchTransition>
  );
};

export default TransitionEle;
