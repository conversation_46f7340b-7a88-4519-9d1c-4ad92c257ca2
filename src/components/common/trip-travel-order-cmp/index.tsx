/**
 * 选择商旅服务商
 */
import { Spin, Dialog, Icon, MActionSheet, IconNames } from '@weapp/ui';
import { dayjs, getLabel, qs, vconsole } from '@weapp/utils';
import React, { useEffect, useState, useRef } from 'react';
import isEmpty from 'lodash-es/isEmpty';
import { weappFnaBsTravelClsPrefix, PUBLIC_URL_STATIC } from '../../../constants';
import { getOpenPriceCompareReq, getProviders, getProviderSsoLink } from '../../../api/common/price-comparison';
import { IProviderSsoLinkProps } from '../../../api/common/price-comparison/type';
import { travelWay, getUrlParams, jumpLink } from '../../pc/price-comparison/utils';
import './style/index.less';

const cls = `${weappFnaBsTravelClsPrefix}-service-provider-modal`;
const { globalSpin } = Spin;
const { message } = Dialog;

export interface IProps {
  weId?: string;
  onClose?: () => void;
  pageType: string; // 页面类型 ORDER BOOKING
  visible: boolean;
  needOpenPriceCompare?: boolean; // 是否需要判断开启比价 开启的话 默认跳转到比价页面
  client?: 'PC' | 'MOBILE';
  priceCompareInfo: {
    productType: number;
    rowId: string;
    requestId: string;
    [x: string]: any;
  }; // 比价信息
  getInstance?: (instance: any) => void; // 获取实例
}
interface TrafficTypeListItemProps {
  id: string;
  name: string;
  icon?: string;
}
interface ItemProps {
  id: string;
  content: string;
  icon: string;
  trafficTypeList?: TrafficTypeListItemProps[];
}
const travelItemIcon: any = {
  TICKET: 'Icon-aircraft',
  HOTEL: 'Icon-get-accommodation-o',
  TRAIN: 'Icon-train',
};
const useGetState = (initVal: any) => {
  const [state, setState] = useState(initVal);
  const ref = useRef(initVal);
  const setStateCopy = (newVal: any) => {
    ref.current = newVal;
    setState(newVal);
  };
  const getState = () => {
    return ref.current;
  };
  return [state, setStateCopy, getState];
};

const initVconsole = () => {
  const urlSearchParams: any = getUrlParams();
  if (!urlSearchParams.showConsole) return;
  let vConsole = null;
  vconsole().then(m => {
    vConsole = new m.default();
  });
};
const ServiceProviderModal: React.ComponentType<IProps> = props => {
  const [show, setShow] = useState(false); // 控制是否显示弹窗 存在一个服务商直接跳转了 不显示弹窗
  const [provider, setProvider, getProvider] = useGetState([] as ItemProps[]);
  const [pending, setPending] = useState(false);
  const [isLink, setIsLink, getIsLink] = useGetState(false);
  const _cls = props.client === 'MOBILE' ? `${cls}-mobile` : `${cls}-pc`;
  useEffect(() => {
    if (!props.pageType) {
      throw new Error('please set pageType');
    }
    initVconsole();
  }, []);
  useEffect(() => {
    const { visible } = props;
    if (visible) {
      initial();
    }
    return () => {
      onClose();
    };
  }, [props.visible]);
  const messageTip = (type: 'info' | 'error' | 'success', content: any) => {
    message({ type, content });
  };
  // 校验日期是否过期
  // 1、飞机票的依据是返回日期大于当前日期
  // 2、订旅店的依据是离店日期比如大于当前日期，不能为同一天
  const validDateIsOutOfDay = (productType: Number) => {
    let canBiJia = false;
    const { priceCompareInfo: info } = props;
    if (productType === 1) {
      const validDate = dayjs(info.fhrq).endOf('day').valueOf();
      const currentDate = dayjs().valueOf();
      canBiJia = validDate > currentDate;
    }
    if (productType === 2) {
      const validDate = dayjs(info.ldrq).startOf('day');
      const currentDate = dayjs().startOf('day');
      canBiJia = validDate.isAfter(currentDate);
    }
    return canBiJia;
  };
  const initial = async () => {
    const { priceCompareInfo: info, needOpenPriceCompare } = props;
    globalSpin.start();
    const _providers = await getProviderReq();
    if (_providers) {
      setProvider(_providers);
      if (needOpenPriceCompare && (+info.productType === 1 || +info.productType === 2)) {
        // 需要判断当前日期是否过期
        if (validDateIsOutOfDay(+info.productType)) {
          // 未过期，走比价逻辑
          getOpenPriceCompare();
        } else {
          // 直接唤起服务商选择弹窗
          messageTip('info', getLabel('269029', '不支持对历史日期进行比价'));
          handleProviderDialog();
        }
      } else {
        handleProviderDialog();
      }
    }
  };
  const handleSetShow = (value: boolean) => {
    if (props.client === 'MOBILE') {
      renderActionSheet();
      return;
    }
    setShow(value);
  };
  const handleProviderDialog = () => {
    const _providers = getProvider();
    if (_providers.length === 1) {
      // 只有一个服务商，直接跳转
      redirectTo(_providers[0]);
      onClose();
      return;
    }
    handleSetShow(true);
  };
  const dealPayload = (payload: any) => {
    if (+payload.productType === 1) {
      // 出发日期小于当前日期，且当前日期小于返回日期
      const currentDate = dayjs().startOf('day');
      const departDate = dayjs(payload.cfrq).startOf('day');
      if (departDate.isBefore(currentDate)) {
        payload.cfrq = dayjs().format('YYYY-MM-DD');
      }
    }
    return payload;
  };
  // 获取是否开启比价
  const getOpenPriceCompare = async () => {
    // 只有一个服务商，直接跳转
    const errTip = (msg?: string) => messageTip('error', msg || getLabel('267998', '获取比价信息失败'));
    try {
      const { productType } = props.priceCompareInfo || {};
      const json = {} as any;
      //获取是否开启比价默认需要带上出行方式
      if (productType) {
        json.trafficType = travelWay[+productType];
      }
      const response = await getOpenPriceCompareReq(json);
      if (+response.code === 200) {
        // 开启比价
        if (response.data?.openPriceCompare) {
          const payload = props.priceCompareInfo || {};
          // /sp/fnabstravel/priceComparison?productType=1&rowId=471592477075144598&requestId=999545121601830913&dcwf=0&cfrq=2024-05-17&fhrq=2024-05-20&cfd=成都&mdd=深圳
          const linkUrl = `${PUBLIC_URL_STATIC}/${props.client === 'MOBILE' ? 'mobile' : 'sp'}/fnabstravel/priceComparison?${qs.stringify({
            ...dealPayload(payload),
            pageType: props.pageType,
          })}`;
          jumpLink(linkUrl);
          onClose();
        } else {
          // 开启服务商提供弹窗
          handleProviderDialog();
        }
        globalSpin.destroy();
        return;
      }
      throw new Error(response.msg);
    } catch (error) {
      // @ts-ignore
      errTip(error?.message);
      globalSpin.destroy();
      onClose();
    }
  };
  const getProviderReq = async () => {
    const errTip = (msg?: string) => messageTip('error', msg || getLabel('267999', '获取服务商失败'));
    try {
      const { productType, requestId } = props.priceCompareInfo || {};
      const params: any = {
        deviceType: props.client === 'MOBILE' ? 'H5' : 'PC',
        requestId,
      };
      //获取服务商默认需要带上出行方式
      if (productType) {
        params.trafficType = travelWay[+productType];
      }
      const response = await getProviders(params);
      if (+response.code === 200) {
        const _providers = response.data.map((item: any) => {
          if (!isEmpty(item.trafficTypeList)) {
            item.trafficTypeList = item.trafficTypeList.map((i: any) => {
              return {
                ...i,
                icon: travelItemIcon[i.id],
              };
            });
          }
          return item;
        });
        if (isEmpty(_providers)) {
          // todo 没有服务商
          // messageTip('info', getLabel('0', '暂无可供选择的商旅服务商'));
          onClose();
          return;
        }
        return _providers;
      }
      throw new Error(response.msg);
    } catch (error) {
      // @ts-ignore
      errTip(error?.message);
      onClose();
    } finally {
      globalSpin.destroy();
    }
  };
  const getSsoLink = async (item: ItemProps, childItem?: TrafficTypeListItemProps) => {
    const errTip = (msg?: string) => messageTip('error', msg || getLabel('268008', '服务商跳转信息获取失败'));
    try {
      if (props.client === 'MOBILE') {
        globalSpin.start();
      } else {
        setPending(true);
      }
      const { priceCompareInfo: info, pageType, client } = props;
      const { productType, requestId, rowId, cfd, mdd, cfrq, fhrq, rzcs, rzrq, ldrq } = info || {};
      let params: IProviderSsoLinkProps = {
        pageType: pageType,
        isMobile: client === 'MOBILE',
        pallet: item.id,
        tripWay: 1, //1 单程 2往返 目前写死固定值 1
      };
      if (!isEmpty(childItem)) {
        params.trafficType = childItem?.id;
      } else {
        // 没有出行方式 默认给枚举
        params.trafficType = travelWay[+productType];
      }
      if (pageType === 'BOOKING') {
        params = {
          ...params,
          bizNo: requestId ?? '',
          journeyNo: rowId ?? '',
        };
      }
      if (params.trafficType === 'TICKET' || params.trafficType === 'TRAIN') {
        params = {
          ...params,
          depCity: cfd,
          arrCity: mdd,
          startDate: cfrq,
          endDate: fhrq,
        };
      } else if (params.trafficType === 'HOTEL') {
        params = {
          ...params,
          arrCity: rzcs,
          startDate: rzrq,
          endDate: ldrq,
        };
      }
      const response = await getProviderSsoLink(params);
      if (+response.code === 200) {
        return response.data?.linkUrl;
      }
      throw new Error(response.msg);
    } catch (error) {
      // @ts-ignore
      errTip(error?.message);
      onClose();
    } finally {
      setPending(false);
      globalSpin.destroy();
    }
  };
  const redirectTo = async (item: ItemProps, childItem?: TrafficTypeListItemProps) => {
    const linkUrl = await getSsoLink(item, childItem);
    if (linkUrl) {
      onClose();
      jumpLink(linkUrl);
    }
  };
  const onClose = () => {
    props.onClose && props.onClose();
    setShow(false);
    setIsLink(false);
  };
  const renderProviderItem = (item: ItemProps) => {
    if (props.client === 'MOBILE') {
      const hasChild = !isEmpty(item.trafficTypeList);
      const renderChild = (childItem: TrafficTypeListItemProps) => {
        return (
          <div className={`${_cls}-item-children-item`} key={childItem.id} onClick={e => redirectTo(item, childItem)}>
            <div className={`${_cls}-item-children-item-icon ${childItem.icon}`}>
              <Icon weId={`${props.weId || ''}_5ijrcq`} name={childItem.icon as IconNames} size="lg" />
            </div>
            <div className={`${_cls}-item-children-item-name`}>{childItem.name}</div>
          </div>
        );
      };
      return (
        <div
          key={item.id}
          className={`${_cls}-item`}
          onClick={e => {
            if (hasChild) {
              // 点击子项 控制ActionSheet关闭
              const childItem = document.getElementsByClassName(`${_cls}-item-children-item`);
              // @ts-ignore
              if (childItem && childItem.length && e && e.target && Array.from(childItem).find(i => i.contains(e.target))) {
                setIsLink(true);
              } else {
                e && e.stopPropagation();
              }
            } else {
              setIsLink(true);
              redirectTo(item);
            }
          }}
        >
          <div className={`${_cls}-item-main`}>
            <div className={`${_cls}-item-main-left`}>
              <div className={`${_cls}-item-main-icon`}>
                <Icon weId={`${props.weId || ''}_3lcul4`} name={item.icon as IconNames} size="lg" />
              </div>
              <div className={`${_cls}-item-main-name`}>{item.content}</div>
            </div>
            <div className={`${_cls}-item-main-right`}>
              {isEmpty(item.trafficTypeList) && <Icon weId={`${props.weId || ''}_4tivuj`} name="Icon-Right-arrow01" />}
            </div>
          </div>
          {hasChild && <div className={`${_cls}-item-children`}>{(item.trafficTypeList || []).map(i => renderChild(i))}</div>}
        </div>
      );
    }
    return (
      <div key={item.id} className={`${_cls}-item`} onClick={() => redirectTo(item)}>
        <div className={`${_cls}-item-wrap`}>
          <div className={`${_cls}-item-icon`}>
            {/* @ts-ignore */}
            <Icon weId={`${props.weId || ''}_2tulzg`} name={item.icon} alt={item.content} size="lg" />
          </div>
        </div>
        <div className={`${_cls}-item-name`}>{item.content}</div>
      </div>
    );
  };
  const renderActionSheet = () => {
    let BUTTONS = getProvider().map((i: ItemProps) => {
      return {
        ...i,
        content: renderProviderItem(i),
      };
    });
    const callback = (index: number, rowIndex?: number) => {
      return new Promise(async (resolve, reject) => {
        if (index === -1 || getIsLink()) {
          resolve(true);
          onClose();
        }
      });
    };
    MActionSheet.showActionSheetWithOptions(
      {
        options: BUTTONS,
        message: (
          <div className={`${_cls}-actionSheet-custom-title`}>
            <div />
            <div>{getLabel('268000', '请选择服务商')}</div>
            <div />
            {/* <div onClick={onClose}><Icon weId={`${props.weId || ''}_j3uk78`} name='Icon-error01' size='md' /></div> */}
          </div>
        ),
        maskClosable: true,
        className: `${_cls}-actionSheet`,
      },
      callback
    );
  };
  if (!show || props.client === 'MOBILE') {
    return null;
  }
  return (
    <Dialog
      weId={`${props.weId || ''}_u8z651`}
      visible={show}
      title={getLabel('268001', '请选择商旅服务商')}
      footer={null}
      closable
      destroyOnClose
      mask
      onClose={onClose}
      wrapClassName={cls}
    >
      <Spin weId={`${props.weId || ''}_6rj66a`} spinning={pending}>
        <div className={`${_cls}-content`}>{getProvider().map((item: ItemProps) => renderProviderItem(item))}</div>
      </Spin>
    </Dialog>
  );
};

export default ServiceProviderModal;
