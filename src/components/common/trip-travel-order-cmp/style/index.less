@import '../../../../style/prefix.less';

.@{weappFnaBsTravelClsPrefix}-service-provider-modal {
    .ui-dialog-content{
        width: initial !important;
        max-width: 880px;
    }
    &-pc {
        &-content{
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        &-item{
            width: 200px;
            height: 154px;
            border-radius: 6px;
            margin-right: 16px;
            text-align: center;
            cursor: pointer;
            font-size: var(--font-size-14);
            background: #fff;
            padding: 26px 0 16px 0;
            & > div{
                transition: all .3s;
            }
            &:hover{
                .@{weappFnaBsTravelClsPrefix}-service-provider-modal-pc-item-icon{
                    box-shadow: 0px 8px 20px 0px rgba(9,48,116,0.16);
                    border-color: transparent;
                }
                .@{weappFnaBsTravelClsPrefix}-service-provider-modal-pc-item-name{
                    color: var(--primary);
                }
            }
            &-wrap{
                padding: 0 68px;
            }
            &-icon{
                width: 64px;
                height: 64px;
                border-radius: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid rgba(229,229,229,1);
                transition: all .3s;
                .ui-icon-lg{
                    width: 40px;
                    height: 40px;
                }
            }
            &-name{
                text-align: center;
                margin-top: 20px;
                font-weight: bold;
            }
            &:last-child{
                margin-right: 0;
            }
        }
        &-spin{
            display: flex;
            justify-content: center;
            padding-top: 30px;
        }
    }
    &-mobile {
        &-actionSheet{
            &-custom-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 16px;
                & > div:nth-child(2) {
                    color: var(--main-fc);
                    font-size: 15px;
                    position: relative;
                }
            }
            .ui-m-dialog-mask{
                background: rgba(0,0,0,0.3);
            }
            .ui-m-action-sheet-message{
                padding: 20px 0;
            }
            .ui-m-dialog-content{
                padding-bottom: 20px;
                background: #F0F1F4;
            }
            .ui-m-dialog-top, .ui-m-action-sheet-message, .ui-m-action-sheet-button-list-main-content{
                background: #F0F1F4;
            }
            .ui-m-dialog-content, .ui-m-dialog-top-bottom{
                border-radius: 16px 16px 0 0;
            }
            .ui-m-action-sheet-button-list-item-line{
                display: none;
            }
            .ui-m-action-sheet-button-list-box{
                margin-bottom: 10px;
            }
            .ui-m-action-sheet-button-list-item{
                height: initial;
                line-height: inherit;
                margin: 0 10px;
                overflow-x: initial;
                overflow-y: initial;
            }
            .ui-m-action-sheet-button-list-main{
                max-height: initial;
            }
        }
        &-item{
            padding: 12px 16px;
            background-image: linear-gradient(180deg, rgba(255,255,255,0.80) 0%, rgba(255,255,255,0.40) 100%);
            border-radius: 10px;
            margin-bottom: 10px;
            &-main{
                display: flex;
                align-items: center;
                justify-content: space-between;
                &-left{
                    display: flex;
                    align-items: center;
                }
                &-icon{
                    width: 38px;
                    height: 38px;
                    border: 0.5px solid rgba(227,228,238,1);
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .ui-icon-lg{
                        width: 26px;
                        height: 26px;
                    }
                }
                &-name{
                    margin-left: 10px;
                    color: var(--main-fc);
                    font-size: var(--font-size-14);
                }
                &-right{
                    .Icon-Right-arrow01{
                        color: #999999;
                    }
                }
            }
            &-children{
                padding-top: 12px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                &-item{
                    width: 33%;
                    height: 84px;
                    background: rgba(220,221,232,0.16);
                    border: 0.5px solid rgba(227,228,238,1);
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    margin-right: 10px;
                    &:last-child{
                        margin-right: 0;
                    }
                    &-icon{
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-bottom: 10px;
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        &.Icon-aircraft{
                            background: #e0e4f7;
                        }
                        &.Icon-train{
                            background: #d9eef3;
                        }
                        &.Icon-get-accommodation-o{
                            background: #f5e8e1;
                        }
                        .Icon-aircraft{
                            color: #4672f6
                        }
                        .Icon-train{
                            color: #05bfcf
                        }
                        .Icon-get-accommodation-o{
                            color: #fc7918
                        }
                    }
                    &-name{
                        color: var(--main-fc);
                        font-size: var(--font-size-12);
                    }
                }
            }
        }
    }
}