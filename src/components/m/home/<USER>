import { observable, action,runInAction, toJS } from 'mobx';
import { AnyObj, MDialog,FormStore } from '@weapp/ui';
import { getLabel }  from '@weapp/utils';
import {CallBackFunc} from './types';


const { toast } = MDialog;


export class HomeStore {
    [_:string]:any;

    @observable loading:boolean = false; //整个页面loading 

    

    @action
    updateAttr = (updateItem:{[_: string]: any}) => {
        for (let key in updateItem) {
            if(this.hasOwnProperty(key)){
                this[key] = updateItem[key];
            }
        }
    }





}

const homeStore = new HomeStore();

export default homeStore;
