import React, { ReactNode, Suspense } from 'react';
import { observe, toJS, values } from 'mobx';
import { Link, withRouter } from 'react-router-dom';
import { observer, inject,Provider } from 'mobx-react';
import { middleware, getLabel,ua } from '@weapp/utils';


/* typescript interface */
import { HomeProps } from './types';

/*
* 异步组件可以产生文件相互关联引用，请保持一个 import() 动态入口
* lib.js -> import('routes/demo') -> demo/index.tsx -> lib.js
*/


// @inject('homeStore','mMainStore')
// @middleware('weappHome', 'Home')
@observer
class Home extends React.Component<HomeProps> {
    readonly state = {

    }

    componentDidMount() {
        
    
    }

    componentWillUnmount(){

    }



    render() {
       
        return (
                <div >
                    home
                </div> 

        )
    }
}

export default withRouter(Home);




