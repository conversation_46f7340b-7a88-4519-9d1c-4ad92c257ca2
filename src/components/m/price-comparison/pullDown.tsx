import React, { useRef} from 'react';

interface IProps {
  pullToRefresh: any
}
const PullDownCmp: React.ComponentType<IProps> = props => {
  const lvRef = useRef<HTMLDivElement>(null);
  let child = props.children;
  if (props.pullToRefresh) {
    child = React.cloneElement(
      props.pullToRefresh,
      {
        getScrollContainer: () => lvRef.current,
      },
      child
    );
  }
  return (
    <div
      ref={lvRef}
      style={{
        overflowY: 'auto',
        height: '100%',
      }}
    >
      {child}
    </div>
  );
};

export default PullDownCmp;
