/**
 * 差旅比价页
 */
import { Spin, MInput, Icon, CorsComponent, MCalendar, Dialog, PullToRefresh, Empty } from '@weapp/ui';
import { getLabel, dayjs, setTitle } from '@weapp/utils';
import React, { useEffect, useState, useMemo } from 'react';
import { CSSTransition } from 'react-transition-group';
import isEmpty from 'lodash-es/isEmpty';
import { weappFnaBsTravelClsPrefix } from '../../../constants';
import { getProviders } from '../../../api/common/price-comparison';
import {
  getWeekData,
  isDateInRange,
  setH5Offset,
  getH5BtnConfig,
  getH5SortList,
  scrollToTop,
  getMaskExtraCls,
  getDefaultSortWay,
} from './utils';
import {
  getUrlParams,
  TravelOrder,
  handleChangeOrderInfo,
  TAB_LIST,
  AIR_ORDER_INFO,
  H5_HOTEL_ORDER_INFO,
  useGetState,
  getSort,
  handleGetData,
  formatDate,
  travelWay,
  getValidPriceList,
} from '../../../components/pc/price-comparison/utils';
import LazyLoadImg from '../../common/lazyLoadImg';
import Slider from './comps/slider';
// import PullDownCmp from './pullDown';
import './style/index.less';

const cls = `${weappFnaBsTravelClsPrefix}-MPriceComparison`;
const { message } = Dialog;
const { globalSpin } = Spin;
export interface IProps {
  weId?: string;
}
const PriceComparison: React.ComponentType<IProps> = props => {
  const [airOrder, setAirOrder, getAirOrder] = useGetState(AIR_ORDER_INFO()); // 订机票
  const [hotelOrder, setHotelOrder, getHotelOrder] = useGetState(H5_HOTEL_ORDER_INFO()); // 订酒店
  const [channel, setChannel, getChannel] = useGetState('bizFlight'); // bizFlight bizHotel
  const [pending, setPending] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [weekData, setWeekData] = useState([] as any[]);
  const [showSortDetail, setShowSortDetail] = useState(false);
  const [listData, setListData, getListData] = useGetState([]);
  const [originListData, setOriginListData, getOriginListData] = useGetState([]);
  const [provider, setProvider, getProvider] = useGetState([]);
  const [rangeParams, setRangeParams, getRangeParams] = useGetState({});
  const [freezeOrder, setFreezeOrder, getFreezeOrder] = useGetState({}); // 冻结参数
  const [ordering, setOrdering] = useState(false);
  const [errText, setErrText] = useState('');
  const [lastTime, setLastTime] = useState(''); // 最后一次更新时间

  useEffect(() => {
    setTitle({ title: getLabel('268026', '商旅比价') });
    setParams();
    getProviderReq();
    getData();
    return () => {};
  }, []);
  const messageTip = (type: 'info' | 'error' | 'success', content: any) => {
    message({ type, content });
  };
  const setParams = () => {
    const urlSearchParams: any = getUrlParams();
    // 飞机票
    if (+urlSearchParams.productType === 1) {
      setChannel('bizFlight');
      setAirOrder({
        ...airOrder,
        from: urlSearchParams?.cfd,
        to: urlSearchParams?.mdd,
        departDate: urlSearchParams?.cfrq,
        returnDate: urlSearchParams?.fhrq,
        isReturn: +urlSearchParams?.dcwf === 2,
      });
      handleWeekData(new Date(urlSearchParams?.cfrq));
    } else if (+urlSearchParams.productType === 2) {
      // 酒店票
      setChannel('bizHotel');
      setHotelOrder({
        ...hotelOrder,
        to: urlSearchParams?.rzcs,
        checkInDate: urlSearchParams?.rzrq,
        checkOutDate: urlSearchParams?.ldrq,
      });
    }
    const rangeParams =
      +urlSearchParams.productType === 1
        ? {
            minDate: urlSearchParams.cfrq ? new Date(urlSearchParams.cfrq) : undefined,
            maxDate: urlSearchParams.fhrq ? new Date(urlSearchParams.fhrq) : undefined,
          }
        : {
            minDate: urlSearchParams.rzrq ? new Date(urlSearchParams.rzrq) : undefined,
            maxDate: urlSearchParams.ldrq ? new Date(urlSearchParams.ldrq) : undefined,
          };
    setRangeParams(rangeParams);
  };
  // 获取服务商
  const getProviderReq = async () => {
    const errTip = (msg?: string) => messageTip('error', msg || getLabel('267999', '获取服务商失败'));
    try {
      const pageParams: any = getUrlParams();
      const response = await getProviders({
        deviceType: 'H5',
        requestId: pageParams?.requestId,
        trafficType: travelWay[+pageParams?.productType],
      });
      if (+response.code === 200) {
        const _providers = response.data;
        setProvider(_providers || []);
        return;
      }
      throw new Error(response.msg);
    } catch (error) {
      // @ts-ignore
      errTip(error?.message);
    } finally {
    }
  };
  const getData = async (isRefresh?: boolean) => {
    try {
      setPending(true);
      setListData([]);
      const _channel = getChannel();
      const response = await handleGetData(_channel, getAirOrder(), getHotelOrder(), isRefresh);
      switch (response.code) {
        case 200:
          const { flightList, hotelList, lastTime } = response.data;
          setOriginListData(_channel === 'bizFlight' ? flightList : hotelList);
          sortList(_channel === 'bizFlight' ? flightList : hotelList);
          setLastTime(lastTime);
          break;
        case 269044:
        case 269045:
          setErrText(response.msg);
          break;
        default:
          break;
      }
    } catch (error) {
    } finally {
      setPending(false);
    }
  };
  // 处理排序
  const sortList = (list: any) => {
    const _channel = getChannel();
    const _order = _channel === 'bizFlight' ? getAirOrder() : getHotelOrder();
    // 如果设置了价格区间 先处理一遍范围内的价格
    let result = getValidPriceList(list, _order);
    const hasPriceFilter = !!(_channel === 'bizHotel' && (_order.price.min || _order.price.max));
    if (_order.sortWay === 'normal' && !hasPriceFilter) {
      // 综合排序恢复原来的默认排序
      result = getOriginListData();
    } else {
      result = [...result].sort((a: any, b: any) => {
        return getSort(_order, _channel)(a.lowestInfo, b.lowestInfo);
      });
    }
    setListData(result);
  };
  const changeOrderInfo = (key: string, value?: any) => {
    if (pending) {
      return;
    }
    const newData = handleChangeOrderInfo(getChannel(), getAirOrder(), getHotelOrder(), key, value);
    const _channel = getChannel();
    if (_channel === 'bizFlight') {
      setAirOrder(newData);
    } else {
      setHotelOrder(newData);
    }
    const refreshKey = ['hasReturn', 'departDate', 'returnDate', 'checkInDate', 'checkOutDate', 'keyword'];
    if (refreshKey.includes(key)) {
      getData();
    }
    if (key === 'hasReturn') {
      setTimeout(() => {
        setH5Offset();
      }, 300);
    }
    if (key === 'sortKey' || key === 'price') {
      // 每次拿最原始的列表数据去排序
      sortList(getOriginListData());
    }
  };
  const handleSetTab = (tab: string) => {
    setChannel(tab);
  };
  const handleWeekData = (date: Date) => {
    const urlSearchParams: any = getUrlParams();
    const _weekData = getWeekData(urlSearchParams.cfrq, urlSearchParams.fhrq);
    setWeekData(_weekData);
    setTimeout(() => {
      // 定位到当前日期
      setH5Offset();
    }, 100);
  };
  const onCellClick = (date: Date, isCalendar?: boolean) => {
    const rangeParams = getRangeParams();
    if (!isDateInRange(date, rangeParams.minDate, rangeParams.maxDate)) {
      messageTip('info', getLabel('268387', '日期超出范围'));
      return;
    }
    changeOrderInfo('departDate', dayjs(date).format('YYYY-MM-DD'));
    setShowCalendar(false);
    handleWeekData(date);
    if (isCalendar) {
      setH5Offset();
    }
  };
  const closeMask = () => {
    setShowCalendar(false);
    setShowSortDetail(false);
  };
  const getProviderById = (id: string) => {
    return getProvider().find((item: any) => item.id === id);
  };
  const order = async (e: any, item: any) => {
    e && e.stopPropagation();
    try {
      if (ordering) {
        return;
      }
      globalSpin.start();
      setOrdering(true);
      const _channel = getChannel();
      const _order = _channel === 'bizFlight' ? getAirOrder() : getHotelOrder();
      await TravelOrder(item, getChannel(), true, _order.hasReturn);
    } catch (error) {
    } finally {
      setOrdering(false);
      globalSpin.destroy();
    }
  };
  const renderCalendar = () => {
    const _cls = `${cls}-calendarMask`;
    const _channel = getChannel();
    const _airOrder = getAirOrder();
    const _hotelOrder = getHotelOrder();
    const value = _channel === 'bizFlight' ? _airOrder.departDate : _hotelOrder.checkInDate;
    const rangeParams = getRangeParams();
    const style = showCalendar
      ? {
          height: '100%',
          overflow: 'hidden',
        }
      : {
          height: 0,
          position: 'relative',
        };
    return (
      // @ts-ignore
      <div style={style}>
        <CSSTransition weId={`${props.weId || ''}_4uyix1`} classNames={`${_cls}-expand`} timeout={300} unmountOnExit in={showCalendar}>
          <div className={`${_cls} ${_cls}-calendar`} style={{ height: 400 }}>
            <div className={`${_cls}-content`}>
              <MCalendar
                weId={`${props.weId || ''}_1cz1x0`}
                onCellClick={date => onCellClick(date, true)}
                weekStart={1}
                date={value}
                {...rangeParams}
              />
            </div>
          </div>
        </CSSTransition>
      </div>
    );
  };
  const renderSortDetail = () => {
    const fls = `${cls}-sortDetail`;
    const _channel = getChannel();
    const _order = getFreezeOrder();
    // 酒店筛选下拉 动态高度
    const extraCls = getMaskExtraCls(_channel, _order);
    const _cls = `${cls}-sortDetailMask`;
    const style = showSortDetail
      ? {
          height: '100%',
          overflow: 'hidden',
        }
      : {
          height: 0,
          position: 'relative',
        };
    return (
      // @ts-ignore
      <div style={style}>
        <CSSTransition weId={`${props.weId || ''}_4uyix1`} classNames={`${_cls}-expand`} timeout={300} unmountOnExit in={showSortDetail}>
          <div className={`${_cls} ${extraCls}`}>
            <div className={`${_cls}-content ${fls}`}>{renderSort(getFreezeOrder(), true)}</div>
          </div>
        </CSSTransition>
      </div>
    );
  };
  const airFilter = () => {
    const _fls = `${cls}-head-air`;
    const _airOrder = getAirOrder();
    return (
      <>
        <div className={`${_fls}`}>
          <div className={`${_fls}-b1`}>
            <div className={`${_fls}-b1-s1`}>{_airOrder.from}</div>
            <div
              className={`${_fls}-b1-s2 ${_airOrder.hasReturn ? 'isReturn' : ''}`}
              onClick={() => (_airOrder.isReturn ? changeOrderInfo('hasReturn') : {})}
            >
              {/* @ts-ignore */}
              <Icon weId={`${props.weId || ''}_a33xqi`} name="Icon-Automated-workflow02" size="lg" />
            </div>
            <div className={`${_fls}-b1-s3`}>{_airOrder.to}</div>
          </div>
          <div className={`${_fls}-b2`}>
            <div className={`${_fls}-b2-s1`}>
              <div className={`${_fls}-b2-s1-wrap`}>
                {weekData.map(i => (
                  <div
                    className={`${_fls}-b2-s1-item ${
                      dayjs(i.date).valueOf() === dayjs(_airOrder.hasReturn ? _airOrder.returnDate : _airOrder.departDate).valueOf()
                        ? `${_fls}-b2-s1-item-active`
                        : ''
                    }`}
                    key={i.id}
                    onClick={() => onCellClick(new Date(i.date))}
                    ref={i.nodeRef}
                  >
                    <div>{i.dayOfWeek}</div>
                    <div>{dayjs(i.date).format('MM.DD')}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className={`${_fls}-b2-s2`} onClick={() => setShowCalendar(prevState => !prevState)}>
              <div>
                <div>
                  <Icon weId={`${props.weId || ''}_l5dw4j`} name={'Icon-schedule02' as any} size="md" />
                </div>
                <div>{getLabel('268007', '日历')}</div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };
  const hotelFilter = () => {
    const _fls = `${cls}-head-hotel`;
    const _hotelOrder = getHotelOrder();
    return (
      <div className={`${_fls}`}>
        <div className={`${_fls}-wrap`}>
          <div className={`${_fls}-b1`}>{_hotelOrder.to}</div>
          <div className={`${_fls}-divide`} />
          {/* <div className={`${_fls}-b2`} onClick={() => setShowCalendar(prevState => !prevState)}> */}
          {/* 暂不支持切换 */}
          <div className={`${_fls}-b2`} onClick={() => setShowCalendar(prevState => !prevState)}>
            <div className={`${_fls}-b2-item`}>
              <div>{getLabel('268393', '住')}</div>
              <div>{dayjs(_hotelOrder.checkInDate).format('MM-DD')}</div>
            </div>
            <div className={`${_fls}-b2-item`}>
              <div>{getLabel('268394', '离')}</div>
              <div>{dayjs(_hotelOrder.checkOutDate).format('MM-DD')}</div>
            </div>
          </div>
          <div className={`${_fls}-divide`} />
          <div className={`${_fls}-b3`}>
            <MInput
              weId={`${props.weId || ''}_3g8tiz`}
              placeholder={getLabel('268020', '输入位置/酒店名')}
              // onBlur={(value: any) => changeOrderInfo('keyword', value)}
              onPressEnter={(value: any) => changeOrderInfo('keyword', value)}
              prefix={<Icon weId={`${props.weId || ''}_123`} name="Icon-search" size="sm" />}
            />
          </div>
        </div>
      </div>
    );
  };
  const mockRender = () => {
    return (
      <CorsComponent
        weId={`${props.weId || ''}_mq4b2m`}
        app="@weapp/fnabstravel"
        compName="TripTravelOrderComp"
        visible
        needOpenPriceCompare={false}
        pageType="ORDER"
        client="MOBILE"
      />
    );
  };
  // 排序
  const renderSort = (orderInfo: any, isDetail?: boolean) => {
    const _channel = getChannel();
    const _renderDetail = () => {
      // 酒店下价格筛选特殊处理
      if (_channel === 'bizHotel' && orderInfo.sortKey === 'price') {
        const _setPrice = (price: any) => {
          closeMask();
          changeOrderInfo('price', price);
        };
        return <Slider weId={`${props.weId || ''}_9qcbeq`} onOk={_setPrice} orderInfo={orderInfo} />;
      }
      return (
        <div style={{ padding: '0 16px' }}>
          {getH5SortList(orderInfo.sortKey).map(item => {
            const isActive = orderInfo.sortWay === item.id;
            return (
              <div
                key={item.id}
                className={`${cls}-sort-list-item ${isActive ? `${cls}-sort-list-item-active` : ''}`}
                onClick={() => {
                  setShowSortDetail(false);
                  scrollToTop();
                  setFreezeOrder({ ...orderInfo, sortWay: item.id });
                  if (_channel === 'bizHotel') {
                    setHotelOrder(getFreezeOrder());
                  } else {
                    setAirOrder(getFreezeOrder());
                  }
                  sortList(listData);
                }}
              >
                {item.content}
                {isActive && <Icon weId={`${props.weId || ''}_0zhqs4`} name="Icon-correct01" />}
              </div>
            );
          })}
        </div>
      );
    };
    return (
      <div className={`${cls}-sort`}>
        <div className={`${cls}-sort-tab`}>
          <div className={`${cls}-sort-tab-left`}>
            {getH5BtnConfig(_channel).map(item => {
              let isActive = orderInfo.sortKey === item.id || (_channel === 'bizHotel' && orderInfo.price?.max);
              const sortWayItem = item.list.find(i => i.id === orderInfo.sortWay);
              let title = item.content;
              if (isActive && sortWayItem) {
                if (!(_channel === 'bizHotel' && item.id === 'price')) {
                  title = sortWayItem?.content;
                }
              }
              let _icon = null as any;
              if (!isEmpty(item.list)) {
                if (_channel === 'bizHotel' && item.id === 'price') {
                  _icon = <Icon weId={`${props.weId || ''}_ysm524`} name={'Icon-Filterdata'} />;
                } else {
                  _icon = (
                    <Icon weId={`${props.weId || ''}_ysm524`} name={isDetail && isActive ? 'Icon-up-arrow04' : 'Icon-Down-arrow04'} />
                  );
                }
              }
              return (
                <div
                  className={`${cls}-sort-tab-btn ${isActive ? `${cls}-sort-tab-btn-active` : ''}`}
                  key={item.id}
                  onClick={() => {
                    if (!isDetail) {
                      scrollToTop();
                      // 显示详情
                      setShowSortDetail(true);
                    }
                    setFreezeOrder({ ...orderInfo, sortKey: item.id });
                  }}
                >
                  {title}
                  {_icon}
                </div>
              );
            })}
          </div>
          {!isDetail && lastTime ? (
            <div className={`${cls}-sort-tab-right`}>
              {lastTime && (
                <div className={`${cls}-section3-tab-right`}>
                  {`${getLabel('270082', '更新于')}：${dayjs(lastTime).format('MM-DD HH:mm')}`}
                  {/* @ts-ignore */}
                  <Icon weId={`${props.weId || ''}_22bwyz`} name="Icon-Refresh02" onClick={() => getData(true)} />
                </div>
              )}
            </div>
          ) : (
            ''
          )}
        </div>
        {isDetail && _renderDetail()}
      </div>
    );
  };
  const renderCard = (item: any, index: number) => {
    const _channel = getChannel();
    const { lowestInfo = {}, infoList = [] } = item;
    const {
      logoSrc,
      airplaneName,
      aircraftModel,
      departFlightNumber,
      departTime,
      departAirportName,
      departTerminalName,
      arriveTime,
      arriveAirportName,
      arriveTerminalName,
      timeSpanMillis,
    } = lowestInfo;
    const otherInfoList = infoList.filter((item: any) => item.platform !== lowestInfo.platform);
    const lowerItem = infoList.find((_item: any) => _item.platform === lowestInfo?.platform);
    const platItem = (_item: any, price?: string) => {
      const providerItem = getProviderById(_item.platform);
      console.log('*👏👏👏***providerItem****', providerItem);
      if (!providerItem) {
        return '';
      }
      return (
        <div className={`${cls}-card-platItem`} key={`${_item.platform}_${new Date().getTime()}`} onClick={e => order(e, _item)}>
          <Icon weId={`${props.weId || ''}_ukkbvt`} name={providerItem.icon} className={`${cls}-card-platItem-icon`} size="xs" />
          <div className={`${cls}-card-platItem-content`}>{providerItem.content}</div>
          {price ? <div className={`${cls}-card-platItem-price`}>￥{price}</div> : ''}
        </div>
      );
    };
    const cheapItem = () => {
      const { price, platform } = lowerItem;
      const providerItem = getProviderById(platform);
      if (!providerItem) {
        return '';
      }
      return (
        <div className={`${cls}-card-cheapItem`}>
          <div className={`${cls}-card-cheapItem-num`}>
            <div>￥</div>
            <div>{price}</div>
          </div>
          {platItem(lowerItem)}
        </div>
      );
    };
    const footerItem = () => {
      return <div className={`${cls}-card-footer`}>{otherInfoList.map((i: any) => platItem(i, i.price))}</div>;
    };
    let content = <div />;
    if (_channel === 'bizFlight') {
      content = (
        <>
          <div className={`${cls}-card-ct-airName`}>
            <div className={`${cls}-card-ct-airName-icon`}>
              <img src={logoSrc} alt={airplaneName} />
            </div>
            <div className={`${cls}-card-ct-airName-text`}>
              <div>{`${airplaneName}${departFlightNumber}`}</div>
              {aircraftModel ? (
                <>
                  <div className={`${cls}-card-ct-airName-text-divide`} />
                  <div>{aircraftModel}</div>
                </>
              ) : (
                ''
              )}
            </div>
          </div>
          <div className={`${cls}-card-ct-airDetail`}>
            <div className={`${cls}-card-ct-airDetail-airTime`}>
              <div className={`${cls}-card-ct-airDetail-airTime-item`}>
                <div>{departTime}</div>
                <div>{`${departAirportName}${departTerminalName}`}</div>
              </div>
              <div className={`${cls}-card-ct-airDetail-airTime-divide`}>
                <div className={`${cls}-card-ct-airDetail-airTime-divide-text`}>{formatDate(timeSpanMillis, 'MOBILE')}</div>
                <div className={`${cls}-card-ct-airDetail-airTime-divide-line`} />
              </div>
              <div className={`${cls}-card-ct-airDetail-airTime-item`}>
                <div>{arriveTime}</div>
                <div>{`${arriveAirportName}${arriveTerminalName}`}</div>
              </div>
            </div>
            {cheapItem()}
          </div>
        </>
      );
    }
    if (_channel === 'bizHotel') {
      const { lowestInfo } = item;
      if (!lowestInfo) return '';
      const { hotelName, hotelAddress, hotelImg } = lowestInfo;
      content = (
        <>
          <div className={`${cls}-card-ct-hotelBanner ${isEmpty(otherInfoList) ? '' : 'bd-dl'}`}>
            <LazyLoadImg weId={`${props.weId || ''}_h1fg7r`} src={hotelImg} alt={hotelName} width={200} height={340} />
          </div>
          <div className={`${cls}-card-ct-hotelDetail`}>
            <div>
              <div className={`${cls}-card-ct-hotelDetail-name`}>
                <div className={`${cls}-card-ct-hotelDetail-name-text`}>{hotelName}</div>
              </div>
              <div className={`${cls}-card-ct-hotelDetail-address`}>{hotelAddress}</div>
            </div>
            {cheapItem()}
          </div>
        </>
      );
    }
    return (
      <div className={`${cls}-card ${isEmpty(otherInfoList) ? 'bordered' : ''}`} key={index} onClick={e => order(e, lowerItem)}>
        <div className={`${cls}-card-ct ${_channel}`}>{content}</div>
        {isEmpty(otherInfoList) ? '' : footerItem()}
      </div>
    );
  };
  const renderMain = useMemo(() => {
    const list = getListData();
    // return (
    //   <PullDownCmp
    //     weId={`${props.weId || ''}_d11qmv`}
    //     pullToRefresh={<PullToRefresh weId={`${props.weId || ''}_9mmh68`} direction="up" refreshing={false} onRefresh={() => {}} />}
    //   >
    //     {list.map((i: any, index: number) => renderCard(i, index))}
    //   </PullDownCmp>
    // );
    return <div>{list.map((i: any, index: number) => renderCard(i, index))}</div>;
  }, [listData, provider, channel]);
  const renderContent = () => {
    if (!pending && isEmpty(getListData())) {
      return (
        <Empty
          weId={`${props.weId || ''}_y2h798`}
          title={errText || `${getLabel('41515', '暂无数据')}`}
          image={
            <Icon
              weId={`${props.weId || ''}_8a7l9p`}
              style={{ width: 160, height: 160 }}
              // @ts-ignore
              name={'Icon-empty-drafts-mcolor'}
            />
          }
        />
      );
    }
    return (
      <div>
        <div className={`${cls}-main`}>
          {pending && (
            <div className={`${cls}-main-spin`}>
              <Spin weId={`${props.weId || ''}_ia6dpn`} spinning={pending} />
              <div className={`${cls}-main-spin-text`}>{getLabel('269033', '正在加载数据，请稍后...')}</div>
            </div>
          )}
          {renderMain}
        </div>
      </div>
    );
  };
  let orderInfo = getChannel() === 'bizFlight' ? getAirOrder() : getHotelOrder();
  const isMask = showCalendar || showSortDetail;
  return (
    <div className={`${cls}`}>
      {isMask ? <div className={`${cls}-maskBg`} onClick={closeMask} /> : ''}
      <div className={`${cls}-head ${getChannel()}`}>
        <div>{getChannel() === 'bizFlight' ? airFilter() : hotelFilter()}</div>
      </div>
      {!pending && renderSort(orderInfo)}
      {renderContent()}
      {/* {mockRender()} */}
      {renderCalendar()}
      {renderSortDetail()}
    </div>
  );
};

export default PriceComparison;
