import { getLabel, qs } from '@weapp/utils';
import { createRef } from 'react';
import { weappFnaBsTravelClsPrefix } from '../../../constants';

const weekDays = () => [
  getLabel('206574', '周一'),
  get<PERSON><PERSON><PERSON>('206576', '周二'),
  get<PERSON><PERSON><PERSON>('206577', '周三'),
  get<PERSON><PERSON><PERSON>('206578', '周四'),
  get<PERSON><PERSON><PERSON>('206579', '周五'),
  get<PERSON>abel('206580', '周六'),
  getLabel('206581', '周日'),
];
export const getWeek = (day: Date) => {
  return weekDays()[day.getDay() === 0 ? 6 : day.getDay() - 1];
};
// 获取周区间数据
export const getWeekData = (startDate: Date, endDate: Date) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const dates = [];
  if (start > end) {
    throw new Error('Start date must be before end date');
  }
  while (start <= end) {
    const year = start.getFullYear();
    const month = `0${start.getMonth() + 1}`.slice(-2);
    const day = `0${start.getDate()}`.slice(-2);
    dates.push(`${year}-${month}-${day}`);
    start.setDate(start.getDate() + 1);
  }

  const weekData = [];

  for (let i = 0; i < dates.length; i++) {
    const day = new Date(dates[i]);
    weekData.push({
      id: i,
      date: dates[i],
      dayOfWeek: getWeek(day),
      nodeRef: createRef(),
    });
  }
  return weekData;
};
// 获取整周数据 暂废弃
export const getFullWeekData = (date: Date) => {
  const d = new Date(date);
  const dayOfWeek = d.getDay();
  const currentDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
  const monday = new Date(date);
  monday.setDate(monday.getDate() - currentDay);
  const firstDayOfWeek = new Date(d.getFullYear(), d.getMonth(), d.getDate() - dayOfWeek);
  firstDayOfWeek.setHours(0, 0, 0, 0);

  const weekData = [];

  for (let i = 0; i < 7; i++) {
    const day = new Date(monday);
    day.setDate(day.getDate() + i);
    weekData.push({
      id: i,
      date: `${day.getFullYear()}-${day.getMonth() + 1}-${day.getDate()}`,
      dayOfWeek: getWeek(day),
      nodeRef: createRef(),
    });
  }
  return weekData;
};
export const isDateInRange = (checkDate: Date, startDate: Date, endDate: Date) => {
  // 确保传入参数都是 Date 对象
  if (!(checkDate instanceof Date) || !(startDate instanceof Date) || !(endDate instanceof Date)) {
    throw new Error('must be Date object');
  }
  // 比较日期
  return checkDate >= startDate && checkDate <= endDate;
};

export const setH5Offset = () => {
  const cls = `${weappFnaBsTravelClsPrefix}-MPriceComparison`;
  const dom = document.querySelector(`.${cls}-head-air-b2-s1-wrap`);
  const activeNode: any = document.querySelector(`.${cls}-head-air-b2-s1-item-active`);
  if (activeNode && dom) {
    dom.scrollLeft = activeNode.offsetLeft - 16;
  }
};
export const scrollToTop = () => {
  const cls = `${weappFnaBsTravelClsPrefix}-MPriceComparison`;
  const content = document.querySelector(`.${cls}`);
  const headNode = document.querySelector(`.${cls}-head`);
  if (content && headNode) {
    content.scrollTo({ left: 0, top: headNode?.clientHeight, behavior: 'smooth' });
  }
};
const getSortWayByPrefix = (prefix: string) => {
  return [
    {
      id: 'asce',
      content: `${prefix}${getLabel('268388', '由低到高')}`,
    },
    {
      id: 'desc',
      content: `${prefix}${getLabel('268389', '由高到低')}`,
    },
  ];
};
// 获取 H5 排序按钮配置
export const getH5BtnConfig = (channel: string) => {
  if (channel === 'bizFlight') {
    return [
      {
        id: 'price',
        content: getLabel('268012', '价格'),
        list: [
          {
            id: 'asce',
            content: `${getLabel('268012', '价格')}${getLabel('268388', '由低到高')}`,
          },
          {
            id: 'desc',
            content: `${getLabel('268012', '价格')}${getLabel('268389', '由高到低')}`,
          },
        ],
      },
      {
        id: 'timeSpanMillis',
        content: getLabel('268013', '时间'),
        list: [
          {
            id: 'asce',
            content: `${getLabel('268013', '时间')}${getLabel('268390', '升序')}`,
          },
          {
            id: 'desc',
            content: `${getLabel('268013', '时间')}${getLabel('268391', '降序')}`,
          },
        ],
      },
    ];
  }
  return [
    {
      id: 'normal',
      content: getLabel('268053', '综合排序'),
      list: [
        {
          id: 'normal',
          content: `${getLabel('268053', '综合排序')}`,
        },
        ...getSortWayByPrefix(getLabel('268012', '价格')),
      ],
    },
    {
      id: 'price',
      content: getLabel('268012', '价格'),
      list: getSortWayByPrefix(getLabel('268012', '价格')),
    },
  ];
};
// 获取 H5 排序列表
export const getH5SortList = (sortKey: string) => {
  switch (sortKey) {
    case 'price':
      return getSortWayByPrefix(getLabel('268012', '价格'));
    case 'timeSpanMillis':
      return getSortWayByPrefix(getLabel('268013', '时间'));
    case 'normal':
      return [{ id: 'normal', content: `${getLabel('268053', '综合排序')}` }, ...getSortWayByPrefix(getLabel('268012', '价格'))];
    default:
      return [];
  }
};
// 酒店筛选每次切换排序方式时，重置默认排序值
export const getDefaultSortWay = (channel: string, sortKey: string) => {
  if (channel === 'bizHotel') {
    return sortKey === 'normal' ? 'normal' : undefined
  }
  return ''
};
// 酒店筛选下拉 动态高度
export const getMaskExtraCls = (channel: string, _order: any) => {
  if (channel === 'bizHotel') {
    return _order.sortKey === 'normal' ? 'NormalLine' : 'SliderLine'
  }
  return '';
};
