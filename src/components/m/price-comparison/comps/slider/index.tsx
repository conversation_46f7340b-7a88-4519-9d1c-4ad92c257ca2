import React, { useEffect, useState } from 'react';
import { MSlider, Button } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import debounce from 'lodash-es/debounce';
import { weappFnaBsTravelClsPrefix } from '../../../../../constants';

interface IProps {
  weId?: string;
  orderInfo: any;
  onOk: (price: any) => void;
}
const { MRange } = MSlider;
const cls = `${weappFnaBsTravelClsPrefix}-MPriceComparison-sortDetailMask-content-slider`;
const step = 15;
const defaultMaxSlideValue = [0, 100];
export const PRICE_SELECT = () => [
  {
    id: '1',
    content: `0~100`,
    price: {
      min: 0,
      max: 99,
    },
  },
  {
    id: '2',
    content: `100~200`,
    price: {
      min: 100,
      max: 200,
    },
  },
  {
    id: '3',
    content: `200~300`,
    price: {
      min: 200,
      max: 300,
    },
  },
  {
    id: '4',
    content: `300~400`,
    price: {
      min: 300,
      max: 400,
    },
  },
  {
    id: '5',
    content: `400~750`,
    price: {
      min: 400,
      max: 750,
    },
  },
  {
    id: '6',
    content: `750~1100`,
    price: {
      min: 750,
      max: 1100,
    },
  },
  {
    id: '7',
    content: `1100~1500`,
    price: {
      min: 1100,
      max: 1500,
    },
  },
  {
    id: '8',
    content: `1500+`,
    price: {
      min: 0,
      max: 1501,
    },
  },
];
const PriceComparSlider = React.forwardRef<IProps, any>((props, ref) => {
  const { orderInfo: info } = props;
  const [slideValue, setSlideValue] = useState(defaultMaxSlideValue);
  const [select, setSelect] = useState('');
  const isDefault = !info.price.min && !info.price.max
  const isMax = `${select}` === `8`;
  useEffect(() => {
    if (info && info.price && info.price.selectId) {
      handleSetSelect(info.price.selectId)
    }
  }, []);
  const syncSelectValue = (price: any, isInit?: boolean) => {
    const { min, max } = price;
    const _min = min || 0;
    const _max = !max || max === 0 || max >= 1500 ? 1500 : max;
    let select = undefined;
    for (let i = 0; i < PRICE_SELECT().length; i++) {
      const item = PRICE_SELECT()[i];
      if (_min >= item.price.min && _max <= item.price.max && (item.id !== '8' || isInit)) {
        select = item;
        break;
      }
    }
    setSelect(select ? select.id : '');
  };
  const onSliderChange = (value: any) => {
    // 手动改变滑块的时候
    setSlideValue(value);
    syncSelectValue({ min: value[0] * step, max: value[1] * step });
  };
  // 防抖 避免重复渲染
  const _onSliderChange = debounce(onSliderChange, 500);
  const reset = () => {
    setSlideValue(defaultMaxSlideValue);
    setSelect('');
    confirm(defaultMaxSlideValue, true);
  };
  const confirm = (_slideValue = slideValue, isReset?: boolean) => {
    const min = isMax ? 1500 :_slideValue[0] * step;
    const max = isMax ? undefined : _slideValue[1] * step;
    let json = {
      min,
      max,
      selectId: select
    }
    if (isReset) {
      json = {
        // @ts-ignore
        min: undefined,
        max: undefined,
        selectId: '',
      }
    }
    props.onOk(json);
  };
  const handleSetSelect = (id: string) => {
    const selected = PRICE_SELECT().find(item => item.id === id);
    if (selected) {
      setSelect(selected.id)
      const priceValue = selected.price
      const _slideValue = [priceValue.min / step, priceValue.max / step];
      setSlideValue(_slideValue);
    }
  };
  const _props = isMax ? {
    value: [96, 100]
  } : {}
  return (
    <div className={cls}>
      <div className={`${cls}-number ${isMax ? `${cls}-number-isMax` : ''}`}>
        <div className={`${cls}-number-min`}>￥{isMax ? 1500 : slideValue[0] * step}</div>
        <div className={`${cls}-number-max`}>
          ￥{slideValue[1] * step > 1500 ? 1500 : slideValue[1] * step}
          {(isMax || isDefault) ? '+' : ''}
        </div>
      </div>
      <MRange
        weId={`${props.weId || ''}_k0yha0`}
        key={select}
        allowCross={false}
        onChange={_onSliderChange}
        defaultValue={slideValue}
        {..._props}
      />
      <div className={`${cls}-price-select`}>
        {PRICE_SELECT().map(item => {
          return (
            <div
              key={item.id}
              className={`${cls}-price-select-item ${select === item.id ? `${cls}-price-select-item-active` : ''}`}
              onClick={() => handleSetSelect(item.id)}
            >
              {item.content}
            </div>
          );
        })}
      </div>
      <div className={`${cls}-btns`}>
        <Button weId={`${props.weId || ''}_h5hzbf`} size="large" onClick={reset}>
          {getLabel('208540', '重置')}
        </Button>
        <Button weId={`${props.weId || ''}_xswoy5`} type="primary" size="large" onClick={() => confirm()}>
          {getLabel('128260', '查看')}
        </Button>
      </div>
    </div>
  );
});

export default PriceComparSlider;
