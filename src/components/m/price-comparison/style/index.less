@import '../../../../style/prefix.less';

.@{weappFnaBsTravelClsPrefix}-MPriceComparison {
    width: 100%;
    height: 100%;
    font-size: var(--font-size-14);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: auto;
    // background-image: linear-gradient(180deg, #FFFFFF 0%, #F0F1F4 51%);
    background-color: #f6f6f8;
    &-maskBg{
        background: rgba(0,0,0,0.5);
        width: 100%;
        height: 100%;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 2;
    }
    &-calendarMask {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 2;
        width: 100%;
        height: 400px;
        overflow: hidden;
        &-calendar{
            z-index: 4;
            .@{weappFnaBsTravelClsPrefix}-MPriceComparison-bg{
                z-index: 5;
            }
            .@{weappFnaBsTravelClsPrefix}-MPriceComparison-content{
                z-index: 6;
            }
        }
        &-content{
            z-index: 4;
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
            width: 100%;
        }
        &-expand-enter {
            max-height: 0;
            overflow: hidden;
        }
        &-expand-enter-active {
            max-height: 400px;
            overflow: hidden;
            transition: max-height 300ms ease-in-out;
        }
        &-expand-exit {
            max-height: 400px;
            overflow: hidden;
        }
        &-expand-exit-active {
            max-height: 0;
            overflow: hidden;
            transition: max-height 300ms ease-in-out;
        }
    }

    &-head {
        position: relative;
        background: #fff;
        border-radius: 0 0 16px 16px;
        z-index: 1;
        &.bizFlight{
            background-image: linear-gradient(165deg, #cbdffa 0%, #F2F8FF 22%, #FFFFFF 49%);
            box-shadow: 0px 0px 12px 0px rgba(47,53,143,0.06);
            padding-bottom: 16px;
            &::before{
                position: absolute;
                left: 0;
                top: 0;
                content: '';
                width: 100%;
                height: 1px;
                background: #fff;
            }
        }
        &.bizHotel{
            &::before{
                position: absolute;
                content: '';
                width: 100%;
                height: 100%;
                transform: scaleX(-1);
                // background-image: linear-gradient(147deg, #DEEAFA 0%, #F2F8FF 22%, #FFFFFF 49%);
                border-radius: 0 0 16px 16px;
                box-shadow: 2px 4px 8px hsla(0, 0%, 80%, 0.2);
            }
        }
        &-wrap{
            border-radius: 12px 12px 0 0;
            overflow: hidden;
        }
        &-tab{
            overflow: hidden;
            height: 44px;
            width: 100%;
            position: relative;
            &-item{
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 15px;
                color: #888;
                position: absolute;
                height: 100%;
                width: 50%;
                top: 0;
                & > div {
                    z-index: 3;
                }
                &.bizFlight{
                    left: 0;
                    &.unActive{
                        &::before{
                            transform: skew(-30deg);
                            left: -10px;
                        }
                    }
                    &.active{
                        &::before{
                            background-image: linear-gradient(140deg, #DEEAFA 0%, #F2F8FF 51%, #FFFFFF 113%);
                            transform: skew(30deg);
                            left: -22px;
                        }
                    }
                }
                &.bizHotel{
                    right: 0;
                    &.unActive{
                        &::before{
                            transform: skew(30deg);
                            right: -10px;
                        }
                    }
                    &.active{
                        &::before{
                            background-image: linear-gradient(140deg, #DEEAFA 0%, #F2F8FF 51%, #FFFFFF 113%);
                            transform: skew(-30deg) scaleX(-1);
                            right: -22px;
                        }
                    }
                }
                &.active{
                    color: var(--primary);
                    font-weight: bold;
                    &::after{
                        position: absolute;
                        left: 43%;
                        top: 24px;
                        content: '';
                        opacity: 0.2;
                        background: var(--primary);
                        width: 40px;
                        height: 10px;
                        transform: skewX(-30deg);
                        z-index: 3;
                    }
                    &::before{
                        position: absolute;
                        content: '';
                        width: 115%;
                        height: 100%;
                        z-index: 3;
                        box-shadow: 1px 0px 10px rgba(0, 0, 0, 0.1);
                    }
                }
                &.unActive{
                    &::before{
                        position: absolute;
                        content: '';
                        width: 125%;
                        height: 94%;
                        bottom: 0;
                        background-color: #f0f1f4;
                        border-radius: 10px 10px 0;
                        z-index: 0;
                    }
                }
            }
        }
        &-air{
            margin-top: 20px;
            position: relative;
            &-b1{
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 44px;
                margin-bottom: 20px;
                padding: 0 16px;
                font-weight: bold;
                font-size: 22px;
                &:div{
                    margin-right: 6px;
                    &:last-child{
                        margin-right: 0;
                    }
                }
                &-s2{
                    width: 44px;
                    height: 44px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 0.5px solid rgba(227,228,238,1);
                    margin: 0 20px;
                    border-radius: 50%;
                    cursor: pointer;
                    transition: all .3s;
                    &-isReturn{
                        transform: rotate(-90deg)
                    }
                    .ui-icon-lg{
                        color: var(--primary);
                    }
                }
            }
            &-b2{
                height: 60px;
                display: flex;
                &-s1{
                    flex: 1;
                    overflow: hidden;
                    &-wrap{
                        overflow-x: scroll;
                        display: flex;
                        scroll-behavior: smooth;
                        position: relative;
                        &-shadow{
                            &-left{
                                &::after{
                                    content: "";
                                    position: absolute;
                                    top: 0;
                                    left: -10px;
                                    width: 10px;
                                    height: 100%;
                                    box-shadow: 10px 0 10px rgba(0, 0, 0, 0.5);
                                }
                            }
                            &-right{
                                &::before{
                                    box-shadow: 10px 0 10px rgba(0, 0, 0, 0.5);
                                }
                            }
                        }
                    }
                    &-item{
                        width: 64px;
                        height: 60px;
                        border-radius: 10px;
                        margin-left: 10px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        flex-shrink: 0;
                        transition: all .3s;
                        position: relative;
                        background: rgba(240, 241, 244, 0.5);
                        & > div{
                            &:first-child{
                                font-size: var(--font-size-12);
                                color: #999;
                            }
                            &:last-child{
                                margin-top: 10px;
                                font-size: var(--font-size-14);
                                color: #111;
                            }
                        }
                        &:last-child{
                            margin-right: 0;
                        }
                        &-active{
                            background: var(--primary);
                            & > div{
                                color: #fff !important;
                                &:last-child{
                                    font-weight: bold;
                                }
                            }
                        }
                    }
                }
                &-s2{
                    width: 64px;
                    margin: 0 auto;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    font-size: var(--font-size-12);
                    color: var(--primary);
                    & > div {
                        box-sizing: -3px 6px 4px rgba(0, 0, 0, 0.3);
                        & > div {
                            text-align: center
                        }
                    }
                    .ui-icon{
                        margin-bottom: 4px;
                    }
                }
            }
        }
        &-hotel{
            padding: 16px 10px;
            position: relative;
            &-wrap{
                display: flex;
                align-items: center;
                background: #f7f8f9;
                border-radius: 17px;
                height: 38px;
                padding: 0 12px;
            }
            &-divide{
                width: 1px;
                height: 14px;
                margin: 0 10px;
                background-color: #ccc;
            }
            &-b1{
                font-size: var(--font-size-12);
                font-weight: bold;
                color: var(--main-fc);
                max-width: 100px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                flex-shrink: 0;
            }
            &-b2{
                flex-shrink: 0;
                &-item{
                    display: flex;
                    align-items: center;
                    font-size: var(--font-size-12);
                    & > div:first-child{
                        color: var(--regular-fc)
                    }
                    & > div:last-child{
                        color: var(--main-fc);
                        font-weight: 600;
                        margin-left: 10px;
                        position: relative;
                        &::after{
                            content: '';
                            position: absolute;
                            left: -5px;
                            top: 5px;
                            width: 2px;
                            height: 2px;
                            background: var(--regular-fc);
                        }
                    }
                }
            }
            &-b3{
                .ui-m-list-item .ui-m-input-label.ui-m-input-label-5{
                    width: initial;
                }
                .Icon-search{
                    color: var(--secondary-fc);
                }
                .ui-m-input-control > input::placeholder {
                    color: #bbb;
                }
            }
        }
    }
    &-sort {
        background: transparent;
        position: relative;
        &-tab{
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 48px;
            padding: 0 16px;
            &-btn{
                transition: all .3s;
                font-size: var(--font-size-14);
                color: var(--main-fc);
                margin-right: 24px;
                font-weight: bold;
                height: 16px;
                line-height: 16px;
                &:last-child{
                    margin-right: 0;
                }
                .ui-icon{
                    position: relative;
                    top: -1px;
                    margin-left: 4px;
                }
                &-active {
                    color: var(--primary);
                    .Icon-Filterdata{
                        color: var(--primary) !important;
                    }
                }
            }
            &-left{
                display: flex;
                align-items: center;
                .Icon-Filterdata{
                    color: #666;
                    width: 16px;
                    height: 16px;
                }
            }
            &-right{
                color: var(--secondary-fc);
                display: flex;
                align-items: center;
                font-size: var(--font-size-12);
                height: 16px;
                line-height: 16px;
                position: relative;
                top: 1px;
                .ui-icon {
                    margin-left: 10px;
                    cursor: pointer;
                    transition: all .3s;
                    position: relative;
                    top: -1px;
                    &:hover{
                        color: var(--primary);
                    }
                }
            }
        }
        &-list{
            &-item{
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 8px 0 0;
                border-bottom: 1px solid #EBEBEB;
                font-size: var(--font-size-14);
                &:last-child{
                    border-bottom-color: transparent;
                }
                &-active{
                    color: var(--primary);
                }
            }
        }
    }
    &-sortDetail{
        overflow: hidden;
        border-radius: 0 0 14px 14px;
        padding-bottom: 4px;
    }
    &-sortDetailMask {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 3;
        width: 100%;
        height: 132px;
        overflow: hidden;
        &.NormalLine{
            height: 172px;
        }
        &.SliderLine{
            height: 274px;
            .@{weappFnaBsTravelClsPrefix}-MPriceComparison-sort{
                height: 100%;
                display: flex;
                flex-direction: column;
                &-tab{
                    flex-shrink: 0;
                    margin-bottom: 10px;
                }
                .Icon-Filterdata{
                    color: var(--primary);
                }
            }
        }
        &-content{
            z-index: 4;
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
            width: 100%;
            height: 100%;
            &-slider{
                flex: 1;
                padding: 0px 24px 16px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                overflow: hidden;
                &-number{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 6px;
                    width: calc(100% + 24px);
                    position: relative;
                    left: -12px;
                    &-isMax{
                        & > div{
                            color: #999;
                        }
                    }
                }
                &-price-select{
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    margin: 16px -8px -8px -8px;
                    &-item{
                        flex: 1 1 22%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 34px;
                        margin: 0 8px 8px 0;
                        background-color: #f8f8f8;
                        transition: background-color 0.3s;
                        font-size: var(--font-size-12);
                        border-radius: 6px;
                        color: var(--main-fc);
                        &:nth-child(4n){
                            margin-right: 0;
                        }
                        &-active{
                            background: rgba(93,156,236,0.14);
                            color: var(--primary);
                        }
                    }
                }
                &-btns{
                    display: flex;
                    justify-content: space-between;
                    margin: 30px -8px 0;
                    .ui-btn{
                        width: 48.5%;
                        height: 40px;
                        font-size: var(--font-size-14);
                        border-radius: 6px;
                        border: none;
                        &:first-child{
                            background: #F0F1F4;
                            color: #111111;
                        }
                    }
                }
            }
        }
        &-expand-enter {
            max-height: 0;
            overflow: hidden;
        }
        &-expand-enter-active {
            max-height: 100%;
            overflow: hidden;
            transition: max-height 600ms ease-in-out;
        }
        &-expand-exit {
            max-height: 132px;
            overflow: hidden;
        }
        &-expand-exit-active {
            max-height: 0;
            overflow: hidden;
            transition: max-height 100ms ease-in-out;
        }
    }

    &-main {
        padding: 0 10px 16px;
        &.scrolled {
            flex: 1;
            height: 100%;
            overflow-y: scroll;
        }
        &-spin{
            padding-top: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            &-text{
                margin-left: 12px;
                color: var(--secondary-fc);
            }
        }
    }
    &-card{
        border-radius: 10px;
        margin-bottom: 10px;
        background-image: linear-gradient(270deg, #F0F1F4 0%, #F8F9FA 49%, #F0F1F4 100%);
        overflow: hidden;
        box-shadow: 2px 4px 8px hsla(0, 0%, 80%, 0.4);
        &.bordered{
            border-radius: 10px;
        }
        &-ct{
            background: #fff;
            &.bizFlight{
                padding: 16px;
            }
            &.bizHotel{
                display: flex;
            }
            &-airName{
                display: flex;
                align-items: center;
                margin-bottom: 16px;
                &-icon{
                    width: 20px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                &-text{
                    color: var(--regular-fc);
                    font-size: var(--font-size-12);
                    margin-left: 8px;
                    display: flex;
                    align-items: center;
                    height: 12px;
                    line-height: 12px;
                    &-divide{
                        width: 1px;
                        height: 10px;
                        margin: 0 8px;
                        background: var(--regular-fc);
                    }
                }
            }
            &-airDetail{
                display: flex;
                justify-content: space-between;
                &-airTime{
                    display: flex;
                    align-items: center;
                    width: 240px;
                    &-item{
                        max-width: 70px;
                        overflow: hidden;
                        flex-shrink: 0;
                        & > div:first-child{
                            font-size: var(--font-size-18);
                            color: var(--main-fc);
                            margin-bottom: 6px;
                        }
                        & > div:last-child{
                            font-size: var(--font-size-12);
                            color: var(--secondary-fc);
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 2;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                    &-divide{
                        position: relative;
                        margin: 0 16px;
                        text-align: center;
                        &-text{
                            font-size: var(--font-size-12);
                            color: var(--secondary-fc);
                        }
                        &-line{
                            border-bottom: 1px dashed rgba(151,151,151,1);
                            width: 70px;
                            height: 0;
                            position: relative;
                            flex-shrink: 0;
                            &::after{
                                content: "";
                                width: 8px;
                                border-bottom: 1px dashed rgba(151,151,151,1);
                                height: 0;
                                position: absolute;
                                bottom: 2px;
                                right: -4px;
                                transform: rotate(-125deg);
                                scale: 0.8;
                            }
                        }
                    }
                }
            }
            &-hotelBanner{
                width: 100px;
                height: 170px;
                border-radius: 10px;
                overflow: hidden;
                img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                &.bd-dl{
                    border-radius: 10px 10px 10px 0;
                }
            }
            &-hotelDetail{
                flex: 1;
                padding: 12px 10px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .weapp-fna-bstravel-MPriceComparison-card-platItem{
                    justify-content: flex-end;
                }
                &-name{
                    margin-bottom: 7px;
                    &-text{
                        font-size: var(--font-size-16);
                        color: var(--main-fc);
                        font-weight: bold;
                    }
                }
                &-address{
                    font-size: var(--font-size-12);
                    color: var(--secondary-fc);
                }
            }
        }
        &-cheapItem{
            justify-self: flex-end;
            &-num{
                display: flex;
                align-items: flex-end;
                justify-content: flex-end;
                color: #FF4D4F;
                font-weight: bold;
                height: 18px;
                line-height: 18px;
                margin-bottom: 6px;
                & > div:first-child{
                    font-size: 12px;
                    line-height: 14px;
                }
                & > div:last-child{
                    font-size: 18px;
                }
            }
            .@{weappFnaBsTravelClsPrefix}-MPriceComparison-card-platItem-icon{
                .ui-icon-xs{
                    width: 14px;
                    height: 14px;
                }
            }
        }
        &-platItem{
            color: var(--main-fc);
            font-size: var(--font-size-12);
            display: flex;
            align-items: center;
            margin-left: 16px;
            &-icon{
                margin-right: 6px;
                .ui-icon-xs{
                    width: 16px;
                    height: 16px;
                }
            }
            &-content {
                position: relative;
                flex-shrink: 0;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100px;
            }
        }
        &-footer{
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            padding: 10px 16px;
            overflow: hidden;
            margin-bottom: -12px;
            // background: #f3f4f6;
            .weapp-fna-bstravel-MPriceComparison-card-platItem{
                margin-left: 0;
                margin-bottom: 12px;
                &-content {
                    color: var(--regular-fc);
                }
                &-price{
                    margin-left: 12px;
                    position: relative;
                    top: 1px;
                    color: var(--regular-fc);
                    &::after{
                        content: '';
                        position: absolute;
                        left: -6px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 2px;
                        height: 2px;
                        border-radius: 50%;
                        background: var(--secondary-fc);
                    }
                }
            }
        }
    }

    &-footer {
        height: 54px;
        width: 100%;
        border-top: .3px solid var(--diviling-line-color);
        background: var(--base-white);
        color: var(--secondary-fc);
        padding: calc(var(--hd)*5);
        &-content{
            width: 100%;
            display: flex;
            &-item{
                width: 50%;
                text-align: center;
                font-size: var(--font-size-14);
                &:first-child{
                    padding-left: 40px;
                }
                &:last-child{
                    padding-right: 40px;
                }
                &-icon{
                    margin-bottom: 4px;
                }
            }
        }
    }
}
