import React, { Suspense } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { observer, inject } from 'mobx-react';
import { getLabel,ua } from '@weapp/utils';


/* typescript interface */
import { WrapperProps } from './types';

/*
* 异步组件可以产生文件相互关联引用，请保持一个 import() 动态入口
* lib.js -> import('routes/demo') -> demo/index.tsx -> lib.js
*/
// import { RouteDoc, RouteTodo } from '../../lib';



@inject('mMainStore')
@observer
class Wrapper extends React.PureComponent<WrapperProps> {
    readonly state = {

    }


    componentDidMount() {
    }


    componentWillUnmount(){

    }





    render() {
        return (
            <div style={{height: '100%'}}>
                {this.props.children}
            </div> 
        )
    }
}

export default withRouter(Wrapper);
