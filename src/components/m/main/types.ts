import { RouteComponentProps } from 'react-router-dom';
import { ReactNode } from 'react';

export interface CallBackFunc {
  (value?:any): void;
}

export interface MainStoreType {
  updateAttr: (value: object) => void;
}

export interface MainProps extends RouteComponentProps {
  mMainStore: MainStoreType;
}

export interface WrapperProps extends RouteComponentProps {
  children?:string | ReactNode;
  mMainStore: MainStoreType;
}