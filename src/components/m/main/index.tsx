import React, { Suspense } from 'react';
import { Route, withRouter,Switch,Redirect } from 'react-router-dom';
import { observer, inject } from 'mobx-react';
import { middleware, getLabel } from '@weapp/utils';

import { fomatParentPath } from '../../../utils';
import Loading from '../loading';
import { weappFnaMBsTravelClsPrefix } from "../../../constants/indexm";
/* typescript interface */
import { MainProps } from './types';
import Wrapper from './Wrapper';
/*
* 异步组件可以产生文件相互关联引用，请保持一个 import() 动态入口
* lib.js -> import('routes/demo') -> demo/index.tsx -> lib.js
*/
import { RouteHome, RouteMPriceComparison } from '../../../lib';
import './style/index.less';

// @inject('mMainStore')
@middleware('weappFnaM', 'MMain')
@observer
class MMain extends React.Component<MainProps> {

  render() {
    const parentPath: string = fomatParentPath(this.props);
    return (
      <div className={weappFnaMBsTravelClsPrefix} style={{ height: '100%' }}>
        <Wrapper weId={`${this.props.weId || ''}_40it3c`} mMainStore={this.props.mMainStore}>
          <Suspense weId={`${this.props.weId || ''}_bwydm4`} fallback={<Loading weId={`${this.props.weId || ''}_zzndwj`}/>}>
              <RouteHome  weId={`${this.props.weId || ''}_hfnvn1`} name={getLabel('41506',"首页")} />
          </Suspense>
          <Switch weId={`${this.props.weId || ''}_r0kgp3`}>
            <Redirect weId={`${this.props.weId || ''}_eeczx7`} exact from={`${parentPath}`} to={`${parentPath}/home`} />
          </Switch>
          <RouteMPriceComparison weId={`${this.props.weId || ''}_4h7hu3`} />
        </Wrapper>
      </div>
    )
  }
}

export default withRouter(MMain);
