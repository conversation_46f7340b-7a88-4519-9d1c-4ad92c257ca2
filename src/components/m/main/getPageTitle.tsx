import { getLabel } from '@weapp/utils';

//获取页面 的名称
const titlePrefix = '/mobile/fna';
const pageTitleMap = new Map([
    [`${titlePrefix}/home`,getLabel('268146','云报销')],
    [`${titlePrefix}/apply`,getLabel('41507','申请')],
    [`${titlePrefix}/invoice`,getLabel('41475',"发票")],
    [`${titlePrefix}/statistical`,getLabel('41508',"统计")],
    [`${titlePrefix}/my`,getLabel('41509',"我的")],
    [`${titlePrefix}/home/<USER>'81977','编辑首页')],
    [`${titlePrefix}/home/<USER>/setLayout`,getLabel('81977','编辑首页')],
    [`${titlePrefix}/basicSetting`,getLabel('40604','基础设置')],
    [`${titlePrefix}/subject`,getLabel('40605','报销科目维护')],
    [`${titlePrefix}/formManage`,getLabel('40969','表单管理')],
    [`${titlePrefix}/formManage/wfadvancedform`,getLabel('85872','高级模式')],
    [`${titlePrefix}/cost`,getLabel('40535','费用标准')],
    [`${titlePrefix}/cost/costform/costdetail`,getLabel('41105','费用标准明细')],
    [`${titlePrefix}/makeNote`,getLabel('74199','记一笔')],
    [`${titlePrefix}/wfRecycle`,getLabel('40978','流程回收站')],
    [`${titlePrefix}/collectionInformation`,getLabel('83584','收款信息维护')],
    
])

export const getPageTitle =(route:string) =>{
    let title = pageTitleMap.get(route) as any;
    return title;
}
  