import { RouteComponentProps } from 'react-router-dom';
/*
* 单级路由组件，可以动态挂在任何一级路由下
* 需要获取父级路由
*/
function fomatParentPath(props: RouteComponentProps) {
  return props.match.url.replace(/\/$/, () => '');
}

function needLayout() {
  return window.location.pathname.indexOf('/splayout') === 0;
}

function isMobile() {
  return window.location.pathname.indexOf('/mobile') === 0;
}


//设置页面title
const setPageTitle = (title:any) =>{
  if(!!window.TEAMS.currentTenant?.tenantName){
    window.weappUtils.weappSDK.setTitle({title: title + ' - ' + window.TEAMS.currentTenant.tenantName});
  } else {
    window.weappUtils.weappSDK.setTitle({title: title});
  }
}

//获取地址栏的参数
const getUrlParam = (url:string) =>{
  if(url == '') return '';
  let temp1 = url.split('?');
  let pram = temp1[1];
  let keyValue = pram.split('&');
  let obj:any = {};
  for (let i = 0; i<keyValue.length; i++){
      let item = keyValue[i].split('=');
      let key = item[0];
      let value = item[1];
      obj[key] = value;
  }

  return obj;
}

export {
  //其他模块类
  needLayout,

  //本模块业务类
  setPageTitle,
  
  //工具类
  isMobile,
  fomatParentPath,
  getUrlParam,
}