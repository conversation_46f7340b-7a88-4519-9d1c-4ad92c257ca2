/*
 * lib.tsx
 * build 生成 static/js/lib.js 共享给其他应用使用的入口文件
 * [externals]全局变量 weapp[模块名] => weappDemo, ESmodule => @weapp/demo
*/
import React from "react";
import { configure } from 'mobx';
import { Provider } from 'mobx-react';
import { stores, mstores } from './store';

import RouteMain from './routes/pc/main';
import RouteMMain from './routes/m/main';

/* 导出库的样式文件，只在应用内使用不做共享的，请写在 style/index.less */
import './style/lib.less';
import libVersion from './libVersion';

/* mobx 配置 */
configure({ enforceActions: 'always' });

/* 常量导出 */
export * as constants from './constants';

/*
 * 异步方式导出路由，路由中串联使用的组件，将一起被打包到路由下
 * 如非必要不要在 lib.tsx 同步引入路由串联组件，这样会导致修改重新更新 lib.js
 * 请不要对同一个文件在多处使用 import() 动态导入，会导致异步文件关系混乱
 * 打包生成路由单个 js 文件按照如下命名
 * route_${module}_${path}
*/

export const Main = React.lazy(() => import(
  /* webpackChunkName: "route_fnabstravel_main" */
  './components/pc/main/index')) as any;
export const SSO = React.lazy(() => import(
  /* webpackChunkName: "route_fnabstravel_main" */
  './components/pc/sso/index')) as any;  
export const PriceComparison = React.lazy(() => import(
    /* webpackChunkName: "route_fnabstravel_main" */
  './components/pc/price-comparison/index')) as any;
  // 差旅订票业务逻辑组件
export const TripTravelOrderCmp = React.lazy(() => import(
    /* webpackChunkName: "route_fnabstravel_main" */
  './components/common/trip-travel-order-cmp/index')) as any;
  export const MPriceComparison = React.lazy(() => import(
    /* webpackChunkName: "route_fnabstravel_main" */
  './components/m/price-comparison/index')) as any;

export const MMain = React.lazy(() => import(/* webpackChunkName: "route_fna_m_main" */'./components/m/main/index')) as any;
export const Home = React.lazy(() => import(/* webpackChunkName: "route_home" */ './components/m/home/<USER>'));



//pc
export { default as RouteMain } from './routes/pc/main';
export { default as RouteSSO } from './routes/pc/sso';
export { default as RoutePriceComparison } from './routes/pc/price-comparison';


//mobile
export { default as RouteMMain } from './routes/m/main';
export { default as RouteHome } from './routes/m/home/<USER>';
export { default as RouteMPriceComparison } from './routes/m/price-comparison/index';


/*
 * 同步导出业务组件，
 * 如非必要不要在 lib.tsx 同步引入路由串联组件，这样会导致修改重新更新 lib.js
*/
export { default as Loading } from './components/pc/loading';

/*
 * 默认导出 store 共享
 * 由于 store 需要同步注入，这样会导致修改重新更新 lib.js
*/
export { stores, mstores };



// 必须
// 导出是本项目的主路由，外层包上store
export function RouterMain () {
  return <Provider weId={`_93e57y`} {...stores}>
      <RouteMain weId={`_pm5bjc`}/>
  </Provider>
}

// 必须
// 导出是本项目的主路由，外层包上store
export function MRouterMain () {
  return <Provider weId={`_8305qc`} {...mstores}>
      <RouteMMain weId={`_5buhym`} />
  </Provider>
}
export function TripTravelOrderComp (props: any) {
  return <Provider weId={`_8305qc`}>
      <TripTravelOrderCmp weId={`_4spkbk`} {...props} />
  </Provider>
}
export const version = libVersion;