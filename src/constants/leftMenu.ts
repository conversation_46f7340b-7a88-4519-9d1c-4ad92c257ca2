import { getLabel } from '@weapp/utils';
import { needLayout } from "../utils";
import { request,qs } from '@weapp/utils';
import { resolve } from 'dns';

//报销
export const applicationMenu = [
    {
      id: "fnabstravel_home",
      content: getLabel("41367","费用管理"),
      icon:'Icon-Cloud-reimbursement-o',
      moduleId: 'expenseManage',
      path: "",
      needOpenNewPage: false,
      order: 0,
      status: true,
      menuCode: "fna-1",
      children: [],
    },
    {
      id: "fnabstravel_SSO",
      content: getLabel("","单点登录"),
      icon:'Icon-financial-budget-o',
      moduleId: 'sso',
      needOpenNewPage: false,
      order: 0,
      status: true,
      menuCode: "fna-1",
      children:[],
      path: "/fnabstravel/sso",
    },
];

/**
 * 暴露主框架左侧菜单
 */
 export function getLeftMenuConfig() {
  return {
      module: 'fna', //string类型,当前模块,主框架将根据模块标识请求菜单数据
      expandChild: false,//展开所有二级菜单
      show: true,//默认展开
      //接口异常时兜底配置
      mockMenu: {
          getData: getMockMenu,//本模块菜单数据
          expandChild: false,//展开所有二级菜单
          show: true//默认展开
      }
  }
}

/**
 * 菜单异常时的兜底菜单（mock数据，不支持设置审批菜单）
 */
const getMockMenu = () => {
  return applicationMenu;
};

export const getFnaMenuDataCheck = async (call?:any) => {
  let incMenu:any = [];
  let fnaMenu:any = [];
  await getMenuDataCheck({menuCode:'inc'}).then((result:any)=>{
    console.log('resultinc',result);
    incMenu = result;
  });

  await getMenuDataCheck({menuCode:'fexs'}).then((result:any)=>{
    console.log('resultfexs',result);
    fnaMenu = result;
  });

  let fnamenu = filterMenu([...fnaMenu,...incMenu]);

  return fnamenu;
}

const getMenuDataCheck = (data: object) => {

  return request({
    url: '/api/auth/menu/check-menu',
    method: 'GET',
    params:data,
  })
}

const filterMenu = (result: any) => {
    const bsMenuDataTemp: any[] = []
    const menuCodes = result.map((_item: any) => {
        return _item.menuCode
    })
    for (let i = 0; i < applicationMenu.length; i++) {
        let item = applicationMenu[i] as any;
        if (!!item.children && item.children.length > 0) {
            const children: any[] = [];
            item.children.forEach((_childItem: any) => {
                if (menuCodes.includes(_childItem.menuCode) || _childItem.menuCode == 'fna-1' || _childItem.menuCode == '-1') {
                    children.push(_childItem)
                }
            })
            if (children.length > 0) {
                item.children = children
                bsMenuDataTemp.push(item)
            }
        } else {

            if (menuCodes.includes(item.menuCode) || item.menuCode == 'fna-1') {

                bsMenuDataTemp.push(item)
            }
        }

    }

    return bsMenuDataTemp;
}