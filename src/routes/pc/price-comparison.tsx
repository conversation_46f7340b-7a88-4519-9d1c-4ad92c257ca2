import React, { ComponentProps } from 'react';
import { Route, withRouter, RouteComponentProps } from 'react-router-dom';

import { fomatParentPath } from '../../utils';
import { PriceComparison } from '../../lib';

function RoutePriceComparison(props: RouteComponentProps & ComponentProps<any>) {
  const parentPath: string = fomatParentPath(props);
  return (
    <Route weId={`${props.weId || ''}_9ebh20`} path={`${parentPath}/priceComparison`} component={PriceComparison} />
  )
}

export default withRouter(RoutePriceComparison);