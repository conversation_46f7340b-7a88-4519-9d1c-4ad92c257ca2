import React, { Component, ComponentProps } from 'react';
import { Route, withRouter, RouteComponentProps } from 'react-router-dom';
import { fomatParentPath,setPageTitle } from '../../utils';
import { Main } from '../../lib';
import { getLabel } from '@weapp/utils';

class RouteMain extends Component<RouteComponentProps> {
  componentDidMount(){

  }

  render(){
    const parentPath: string = fomatParentPath(this.props);
    setPageTitle(getLabel('268147', '财务商旅'));

    return (
      <Route weId={`${this.props.weId || ''}_q7i444`} path={`${parentPath}/fnabstravel`} component={Main} />
    )
  }
}

export default withRouter(RouteMain);