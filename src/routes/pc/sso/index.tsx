import React, { ComponentProps } from 'react';
import { Route, withRouter, RouteComponentProps } from 'react-router-dom';

import { fomatParentPath } from '../../../utils';
import { SSO } from '../../../lib';

function RouteSSO(props: RouteComponentProps & ComponentProps<any>) {
  const parentPath: string = fomatParentPath(props);
  console.log('parentPath',parentPath)
  return (
    <Route weId={`${props.weId || ''}_9ebh20`} path={`${parentPath}/sso`} component={SSO} />
  )
}

export default withRouter(RouteSSO);