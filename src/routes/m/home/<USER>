import React, { ComponentProps } from 'react';
import { Route, withRouter, RouteComponentProps } from 'react-router-dom';

import { fomatParentPath } from '../../../utils';
import { Home } from '../../../lib';

function RouteHome(props: RouteComponentProps & ComponentProps<any>) {
  const parentPath: string = fomatParentPath(props);

  return (
    <Route weId={`${props.weId || ''}_eeczx7`} path={`${parentPath}/home`} component={Home} />
  )
}

export default withRouter(RouteHome);