import React, { ComponentProps } from 'react';
import { Route, withRouter, RouteComponentProps } from 'react-router-dom';

import { fomatParentPath } from '../../../utils';
import { MPriceComparison } from '../../../lib';

function RouteMPriceComparison(props: RouteComponentProps & ComponentProps<any>) {
  const parentPath: string = fomatParentPath(props);
  return (
    <Route weId={`${props.weId || ''}_9ebh20`} path={`${parentPath}/priceComparison`} component={MPriceComparison} />
  )
}

export default withRouter(RouteMPriceComparison);