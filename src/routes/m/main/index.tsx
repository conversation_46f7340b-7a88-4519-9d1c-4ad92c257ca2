import React, { Component, ComponentProps } from 'react';
import { Route, withRouter, RouteComponentProps } from 'react-router-dom';
import { fomatParentPath, } from '../../../utils';
import { MMain } from '../../../lib';
class RouteMain extends Component<RouteComponentProps> {
  componentDidMount(){
  }

  render(){
    const parentPath: string = fomatParentPath(this.props);
    return (
      <>
        <Route weId={`${this.props.weId || ''}_set7a3`} path={`${parentPath}/fnabstravel`} component={MMain} />
      </>
    )
  }
}

export default withRouter(RouteMain);