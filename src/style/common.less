@import './prefix.less';
*{
  box-sizing:border-box;
}

.@{weappFnaBsTravelClsPrefix}-text-overflow{
  overflow: hidden;
  white-space: nowrap !important;
  text-overflow: ellipsis;
}

.@{weappFnaBsTravelClsPrefix}-hidden{
  visibility: hidden;
}

.@{weappFnaBsTravelClsPrefix}-display{
  display: none;
}

//清除浮动
.@{weappFnaBsTravelClsPrefix}-clear{
  clear: both;
 }
 
.@{weappFnaBsTravelClsPrefix}-red{
  background-color: #FF666A!important;
}

.@{weappFnaBsTravelClsPrefix}-yellow{
  background-color: #FFCD50!important;
}

.@{weappFnaBsTravelClsPrefix}-green{
  background-color: #40D8AD!important;
}

.@{weappFnaBsTravelClsPrefix}-skyblue{
  background-color: #24CEF6!important;
}

.@{weappFnaBsTravelClsPrefix}-blue{
  background-color: #5D9CEC!important;
}

.@{weappFnaBsTravelClsPrefix}-orange{
  background-color: #FFA500!important;
}

.@{weappFnaBsTravelClsPrefix}-lightcoral{
  background-color: #F08080!important;
}

.@{weappFnaBsTravelClsPrefix}-list-approval-light{
  border-radius: 10%;
  border: solid 1px #FFECE1;
  display: inline-block;
  width: 50px;
  text-align: center;
  background-color: #FFECE1;
  color: #FF7D30;
}

.@{weappFnaBsTravelClsPrefix}-list-draft-light{
  border-radius: 10%;
  border: solid 1px #F7F7F7 ;
  display: inline-block;
  width: 50px;
  text-align: center;
  background-color: #F7F7F7 ;
  color: #999999;
}

.@{weappFnaBsTravelClsPrefix}-list-waitpay-light{
  border-radius: 10%;
  border: solid 1px #5D9CEC ;
  display: inline-block;
  width: 50px;
  text-align: center;
  background-color: #5D9CEC ;
  color: #EDF3FC;
}

.@{weappFnaBsTravelClsPrefix}-list-archive-light{
  border-radius: 10%;
  border: solid 1px #40D8AD ;
  display: inline-block;
  width: 50px;
  text-align: center;
  background-color: #DFFAF2 ;
  color: #40D8AD;
}

.@{weappFnaBsTravelClsPrefix}-list-hrmlink{
  color: var(--primary) ;

  &:hover{
    cursor: pointer;
  }
}

.@{weappFnaBsTravelClsPrefix}-list-request-title{
  display: flex;
  align-items: center;
  & > span:nth-child(1){
    margin-right: 10px;
  }
  .@{weappFnaBsTravelClsPrefix}-listdata-requestname-tag-wait{
    color: var(--primary);
  }
}

.@{weappFnaBsTravelClsPrefix}-empty{
  margin-top: 20%;

  .Icon-empty-file{
    width: 100px;
    height: 100px;
  }
}

.@{weappFnaBsTravelClsPrefix}-empty2{
  .Icon-empty-file{
    width: 100px;
    height: 100px;
  }
}

.@{weappFnaBsTravelClsPrefix}-empty3{
  margin-top: 10%;

  .Icon-empty-file{
    width: 100px;
    height: 100px;
  }
}

.@{weappFnaBsTravelClsPrefix}-list-title:hover{
  color: #5D9CEC ;
}

// 半选状态
.@{weappFnaBsTravelClsPrefix}-indeterminate {
  .@{weappFnaBsTravelClsPrefix}-inner {

    background-color: var(--primary);
    border-color: var(--primary);
    &::after {
      top: 50%;
      left: 50%;
      width: 80%;
      height: calc(1 * var(--hd));
      background-color: var(--base-white);
      border: 0;
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
      content: ' ';
    }
  }
}

.@{weappFnaBsTravelClsPrefix}-spin{
  height: 100%;
  position: inherit;
  .ui-spin-container{
    height: 100%;
  }
}

.@{weappFnaBsTravelClsPrefix}-ad-panel{
  padding-bottom: var(--h-spacing-md);
}

.@{weappFnaBsTravelClsPrefix}-list-hover .ui-table-grid-tr:hover{
  cursor: pointer;
}

.@{weappFnaBsTravelClsPrefix}-break-word {
  word-break: break-word;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.@{weappFnaBsTravelClsPrefix}-text-center {
  text-align: center;
  line-height: 30px;
}

.@{weappFnaBsTravelClsPrefix}-overlay {
  z-index: 9999;
  cursor: not-allowed;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.@{weappFnaBsTravelClsPrefix}-overlay-partial {
  z-index: 9999;
  cursor: not-allowed;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

//tag 分类   gray:停用   green：启用  
.gray{height: 22px;display: inline-block;background: #F7F7F7;border-radius: 3px;font-size: calc(var(--font-size-10));color: #999999;text-align: center;padding: 5px 6px 2px;}
.green{height: 22px;display: inline-block;background: #DFFAF2;border-radius: 3px;font-size: calc(var(--font-size-10));color: #40D8AD;text-align: center;padding: 5px 6px 2px;}
.blue{height: 22px;display: inline-block;background: #EDF3FC;border-radius: 3px;font-size: calc(var(--font-size-10));color: #5D9CEC;text-align: center;padding: 5px 6px 2px;}
.red{height: 22px;display: inline-block;background: #FFE8E8;border-radius: 3px;font-size: calc(var(--font-size-10));color: #F86B6A;text-align: center;padding: 5px 6px 2px;}
.orange{height: 22px;display: inline-block;background: #FFECE1;border-radius: 3px;font-size: calc(var(--font-size-10));color: #FF7D30;text-align: center;padding: 5px 6px 2px;}

.@{weappFnaBsTravelClsPrefix}-back-top {
  .ui-back-top{
    bottom: calc(var(--hd)*40);
  }
}

.@{weappFnaBsTravelClsPrefix}-hover {
  cursor: pointer;
  color: var(--primary);
}

.@{weappFnaBsTravelClsPrefix}-t-red{
  color: #FF666A!important;
}

.@{weappFnaBsTravelClsPrefix}-t-yellow{
  color: #FFCD50!important;
}

.@{weappFnaBsTravelClsPrefix}-t-green{
  color: #40D8AD!important;
}

.@{weappFnaBsTravelClsPrefix}-t-skyblue{
  color: #24CEF6!important;
}

.@{weappFnaBsTravelClsPrefix}-t-blue{
  color: #5D9CEC!important;
}

.@{weappFnaBsTravelClsPrefix}-t-orange{
  color: #FFA500!important;
}

.@{weappFnaBsTravelClsPrefix}-t-lightcoral{
  color: #F08080!important;
}

.@{weappFnaBsTravelClsPrefix}-t-primary{
  color: var(--primary) !important;
}