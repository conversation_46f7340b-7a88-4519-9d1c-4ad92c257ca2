import React, { Suspense, Component, ComponentProps } from 'react';
import ReactDOM from 'react-dom';
import { BrowserRouter as Router, Route } from 'react-router-dom';
import { Provider } from 'mobx-react';
import { corsImport,getLocale } from '@weapp/utils';

import './style/index.less';
import { needLayout, isMobile } from './utils';
import * as Lib from '@weapp/fnabstravel';
import reportWebVitals from './reportWebVitals';
import { getLeftMenuConfig,applicationMenu } from './constants/leftMenu';

const { RouteMain, RouteMMain, stores, mstores, Loading } = Lib;

// pc moblie 共享store
const allStore = {
  stores,
  mstores
}

/* 路由根路径：这里的publicUrlweappDemo需要改成对应模块的地址 例如： window.publicUrlweappWorkflow  */
export const root = (window.publicUrlweappDemo || window.publicUrlstatic || window.publicUrl || '');

function Coms(props: any) {
  let path = props.needLayout ? `${root}/splayout/` : `${root}/sp/`;
  if (isMobile()) {
    path = `${root}/mobile/`;
  }
  return <Route weId={`${props.weId || ''}_qe5lp8`} path={`${path}`}>
          <Suspense weId={`${props.weId || ''}_knh1c6`} fallback={<Loading weId={`${props.weId || ''}_6xaomf`} />}>
            {isMobile() ? <RouteMMain weId={`${props.weId || ''}_3fzvxx`} /> : <RouteMain weId={`${props.weId || ''}_jga62u`} />}
          </Suspense>
        </Route>
}

class Main extends Component<ComponentProps<any>, any> {

  state = {
    module: <Coms weId={`${this.props.weId || ''}_joljrn`} />
  }

  componentDidMount(){
    if (needLayout()) {
      this.loadLayout();
    }
  }

  loadLayout() {
    // 可以根据业务模块的实际情况 定义模块根加载路径
    corsImport(`@weapp/layout`).then((app) => {
      if (app.SpComs) {
        this.setState({module: React.createElement(app.SpComs, {}, <Coms weId={`${this.props.weId || ''}_u7kn3s`} needLayout/>)}, () => {
        });
        app?.mainStore?.updateAside(applicationMenu, needLayout());
      }


    }, (error) => {
      console.error(error);
    });
  }

  render(){
    return <>
      {this.state.module}
    </>
    }
}

getLocale('@weapp/fna').then(() => {  //fnabstravel
  ReactDOM.render(
    <React.StrictMode weId={`_ah972a`}>
      <Provider weId={`_13w91d`} {...allStore}>
        <Router weId={`_y47lv0`}>
          <Main weId={`_zjjrk6`} />
        </Router>
      </Provider>
    </React.StrictMode>,
    document.getElementById('root')
  );
})

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();

/*
 * 默认应用本身按照 lib.js 进行 ESmodule 规范导出
 * 应用本页如果需要在全局获取自身的库（二开之类），虽然一般用不到，将被封装到 default 导出，和 lib 之间有差异
*/
export default Lib;
