## [0.1.16](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.15...v0.1.16) (2025-06-06)



## [0.1.15](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.14...v0.1.15) (2025-05-07)



## [0.1.14](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.13...v0.1.14) (2025-04-03)



## [0.1.13](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.12...v0.1.13) (2025-03-07)



## [0.1.12](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.11...v0.1.12) (2025-02-07)



## [0.1.11](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.10...v0.1.11) (2025-01-03)



## [0.1.10](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.9...v0.1.10) (2024-12-06)


### Bug Fixes

* no.2024044044 解决商旅比价列表显示问题 ([42a92b1](http://10.12.101.12/FNA/weapp-fnabstravel/commits/42a92b1f95a572e3160eaeaca403c25e80659979))


### Features

* no.2024044044 新增商旅比价携程商旅兼容往返 ([a0fd462](http://10.12.101.12/FNA/weapp-fnabstravel/commits/a0fd462de9becacbe0246e3b7650c8b170f2ceb3))
* no.2024044044 新增商旅比价携程商旅参数改造 ([2311de0](http://10.12.101.12/FNA/weapp-fnabstravel/commits/2311de06f7c6e5af653d933d4d42b436071166f2))
* no.2024044044 新增商旅比价携程商旅参数改造 ([c53b91b](http://10.12.101.12/FNA/weapp-fnabstravel/commits/c53b91b5b14044aa8fa1ca080dcd66e3c1271136))



## [0.1.9](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.8...v0.1.9) (2024-11-08)



## [0.1.8](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.7...v0.1.8) (2024-10-12)



## [0.1.7](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.6...v0.1.7) (2024-09-06)



## [0.1.6](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.5...v0.1.6) (2024-08-09)


### Bug Fixes

* no.2981575 解决比价列表缓存日期的问题 ([02ab53a](http://10.12.101.12/FNA/weapp-fnabstravel/commits/02ab53aa8e7844208c6c014c77af26cfcebf622d))
* no.3119228 解决移动端价格筛选失效问题 ([55925b8](http://10.12.101.12/FNA/weapp-fnabstravel/commits/55925b8d698a62308be5d89d10bcf89227ace0cf))
* no.55925b8 解决移动端比价列表最大值筛选无效问题 ([f32eaed](http://10.12.101.12/FNA/weapp-fnabstravel/commits/f32eaedc516b3c1bca443c9c5e4fab5383737d67))


### Performance Improvements

* no.3097590 优化pc+h5比价列表样式验收问题 ([31525de](http://10.12.101.12/FNA/weapp-fnabstravel/commits/31525de452b6c6c9446f34e4b1f7aef1a30c60f4))
* no.3097590 优化pc+h5比价列表样式验收问题 ([902486f](http://10.12.101.12/FNA/weapp-fnabstravel/commits/902486f215552f820860a059b90c31cde45d0534))
* no.3097590 优化pc+h5比价列表样式验收问题 ([70b2004](http://10.12.101.12/FNA/weapp-fnabstravel/commits/70b2004c3e416b0ac3d5404784bf654f4aafae91))
* no.3097590 优化pc+h5比价列表样式验收问题 ([105daf4](http://10.12.101.12/FNA/weapp-fnabstravel/commits/105daf44cb46ea08945a924dd1ad4cd6811c05f8))
* no.3097590 优化pc+h5比价列表样式验收问题 ([053aa2e](http://10.12.101.12/FNA/weapp-fnabstravel/commits/053aa2e632c735a1627d0475429960e89a16dbfe))
* no.3097590 优化脚手架版本 ([4502295](http://10.12.101.12/FNA/weapp-fnabstravel/commits/45022959df2aee3068afe2403f02e9261b7b3579))



## [0.1.5](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.4...v0.1.5) (2024-07-15)


### Features

* no.3097590 新增满足了比价页面增加价格筛选的功能 ([9cc9e4e](http://10.12.101.12/FNA/weapp-fnabstravel/commits/9cc9e4eb0e98c00c96221a8fd454584d6bd0f9c1))
* no.3097590 新增移动端比价页面快捷筛选价格 ([0bb7942](http://10.12.101.12/FNA/weapp-fnabstravel/commits/0bb79424a6fed743117a1bad37037feb466eb499))



## [0.1.4](http://10.12.101.12/FNA/weapp-fnabstravel/compare/v0.1.3...v0.1.4) (2024-06-14)


### Bug Fixes

* no.2981575 解决比价列表缓存日期的问题 ([e5aa31c](http://10.12.101.12/FNA/weapp-fnabstravel/commits/e5aa31c44f28b80e8346a5b604700adc90b0485c))
* no.2981575 解决比价我的订单报错 ([0c036b5](http://10.12.101.12/FNA/weapp-fnabstravel/commits/0c036b57a5ed897c55e936240ee3cda5d56463d3))
* no.2981575 解决比价我的订单报错 ([ab9379d](http://10.12.101.12/FNA/weapp-fnabstravel/commits/ab9379d3a5ef567d719810128e21dcdf87d035f7))
* no.2981575 解决比价相关接口增加出行方式参数 ([79aa602](http://10.12.101.12/FNA/weapp-fnabstravel/commits/79aa602e7e5fd56c04e692d7ed3389c06deea888))
* no.2981575 解决移动端列表下滑滚动失效的问题 ([03927e9](http://10.12.101.12/FNA/weapp-fnabstravel/commits/03927e95ec781a68be8163bf0be5296fa4202d2e))
* no.2981575 解决移动端单点登录跳转问题 ([a384f51](http://10.12.101.12/FNA/weapp-fnabstravel/commits/a384f516aa8b7c0457ae4f363a6108f5e5ca9b07))
* no.2981575 解决移动端无法点击其他商旅问题 ([3df22ce](http://10.12.101.12/FNA/weapp-fnabstravel/commits/3df22cee730e77d0d1988397be610655aba550c4))
* no.2981575 解决移动端日期选择限制 ([560bb05](http://10.12.101.12/FNA/weapp-fnabstravel/commits/560bb0526ab56a8a9f3a01bae9f98db7a8a080ac))
* no.2981575 解决移动端服务商选择弹窗无法反复唤起的问题 ([1517219](http://10.12.101.12/FNA/weapp-fnabstravel/commits/1517219ab89ed6170cd217819ef2dbc48e86ac72))
* no.2981575 解决移动端服务商选择弹窗无法反复唤起的问题 ([26306d5](http://10.12.101.12/FNA/weapp-fnabstravel/commits/26306d53f78ff5a5c4b38cf618724da5d3882b57))
* no.2981575 解决移动端比价列表显示 ([6578372](http://10.12.101.12/FNA/weapp-fnabstravel/commits/6578372b034c6371b39493e1872dfe766acd44e6))
* no.2981575 解决移动端比价列表显示 ([73d1cdc](http://10.12.101.12/FNA/weapp-fnabstravel/commits/73d1cdc9447f3200e2a95c087ce8b2ff12e0627f))
* no.2981575 解决移动端点击时增加loading效果 ([28e7f27](http://10.12.101.12/FNA/weapp-fnabstravel/commits/28e7f27c26a2768af7f377e2b42b64fd406a664b))
* no.2981575 解决移动端酒店重复跳转问题 ([feed947](http://10.12.101.12/FNA/weapp-fnabstravel/commits/feed9476143d1b984878203bdff197e8f136aef4))
* no.2981575 解决移除移动端vconsole ([2e2bd5b](http://10.12.101.12/FNA/weapp-fnabstravel/commits/2e2bd5bf207f7af179183e811431c9ae80d75e73))
* no.2981575 解决英文模式下相关显示问题 ([5bce2ee](http://10.12.101.12/FNA/weapp-fnabstravel/commits/5bce2ee0d3ed496aee41d51e35680e8383bf8ed6))


### Features

* no.2981575 新增出发日期低于当前日期优化 ([51ecfed](http://10.12.101.12/FNA/weapp-fnabstravel/commits/51ecfedfc5a252a1e8091db22561a75b844793a3))
* no.2981575 新增出发日期低于当前日期优化 ([2e3683f](http://10.12.101.12/FNA/weapp-fnabstravel/commits/2e3683fc449015aedd8aeaccdbadf59ec87a92a4))
* no.2981575 新增刷新缓存数据 ([ff0300b](http://10.12.101.12/FNA/weapp-fnabstravel/commits/ff0300b1116b6e64c2f131c380074f59fe7d3157))
* no.2981575 新增刷新缓存数据 ([1f18ef3](http://10.12.101.12/FNA/weapp-fnabstravel/commits/1f18ef35308ab52ce61675a4ecd06fa454f32f83))
* no.2981575 新增历史日期判断提示 ([01696bd](http://10.12.101.12/FNA/weapp-fnabstravel/commits/01696bd00a8f2515408002b51ad7bbc84a10da7b))
* no.2981575 新增图片过大时压缩图片并支持点击预览 ([8cacdbe](http://10.12.101.12/FNA/weapp-fnabstravel/commits/8cacdbe0cdf6369ff40a39a36f7ea0accbc8982c))
* no.2981575 新增图片过大时压缩图片并支持点击预览 ([0ab2925](http://10.12.101.12/FNA/weapp-fnabstravel/commits/0ab2925115d8af6d2b68c595a7c175c3f62f760f))
* no.2981575 新增移动端点击跳转区域优化 ([389cbb5](http://10.12.101.12/FNA/weapp-fnabstravel/commits/389cbb5ab2a43054ec8011d13e5edef408f16258))


### Performance Improvements

* no.2981575 优化比价页列表loading效果 ([243763b](http://10.12.101.12/FNA/weapp-fnabstravel/commits/243763b8fabc193866e154228fb0ddc0412ec91c))
* no.2981575 优化比价页列表loading效果 ([08a2544](http://10.12.101.12/FNA/weapp-fnabstravel/commits/08a254488eda7f03a5334104465c717b90f7c49f))
* no.2981575 优化酒店搜索为回车搜索 ([d84c1f5](http://10.12.101.12/FNA/weapp-fnabstravel/commits/d84c1f5a9cc0da2b1b511496cb9ef2884fabb83b))



## 0.1.3 (2024-05-29)


### Bug Fixes

* no.2981575 解决pagetype传参问题 ([e8acaf4](http://10.12.101.12/FNA/weapp-fnabstravel/commits/e8acaf42ded3287c650d8dd5cec9895ba085dc24))
* no.2981575 解决pagetype传参问题 ([4c5f0b5](http://10.12.101.12/FNA/weapp-fnabstravel/commits/4c5f0b5481d049d7d6fe5e2733fd4b6a741e8cb9))
* no.2981575 解决word-break问题 ([605a5ce](http://10.12.101.12/FNA/weapp-fnabstravel/commits/605a5cef7b293c2450767b8eff223bb1be90aafa))
* no.2981575 解决word-break问题 ([b9d0ddf](http://10.12.101.12/FNA/weapp-fnabstravel/commits/b9d0ddfda26540cfe9c3541753d795b746360ef7))
* no.2981575 解决判断单程往返取值 ([c4ec582](http://10.12.101.12/FNA/weapp-fnabstravel/commits/c4ec5824e321cbb27573da0abf37ea03f862d154))
* no.2981575 解决参数问题 ([b604622](http://10.12.101.12/FNA/weapp-fnabstravel/commits/b604622fad4409e2edf788c9494c086e966289f8))
* no.2981575 解决参数问题 ([d4167cd](http://10.12.101.12/FNA/weapp-fnabstravel/commits/d4167cd1311d8680c072352fa1e32c4581e51423))
* no.2981575 解决参数问题 ([341c812](http://10.12.101.12/FNA/weapp-fnabstravel/commits/341c81246ae80ee30937214f03f47034dead5633))
* no.2981575 解决参数问题 ([a5dc6a0](http://10.12.101.12/FNA/weapp-fnabstravel/commits/a5dc6a0d7ba1f6162a2634732d44c62c446f614b))
* no.2981575 解决参数问题 ([4f6dfe6](http://10.12.101.12/FNA/weapp-fnabstravel/commits/4f6dfe67ac8c2981278f3c0f58dcaa461997a4ce))
* no.2981575 解决国际化扫描问题 ([b6baf7f](http://10.12.101.12/FNA/weapp-fnabstravel/commits/b6baf7f64ffb661d3ce8a66fe670c94ba1e16df4))
* no.2981575 解决提供商弹窗接口请求顺序调整 ([aaaadb7](http://10.12.101.12/FNA/weapp-fnabstravel/commits/aaaadb77bc4fca0c840958da0c597a2dc624ef65))
* no.2981575 解决提供商弹窗接口请求顺序调整 ([98a984d](http://10.12.101.12/FNA/weapp-fnabstravel/commits/98a984dfd7493741d5225420db7ee33c8fd74778))
* no.2981575 解决服务商弹窗icon ([204ba3e](http://10.12.101.12/FNA/weapp-fnabstravel/commits/204ba3e9db2b6137eaac60fdb38949c11a694717))
* no.2981575 解决服务商订票按钮逻辑 ([05aedd2](http://10.12.101.12/FNA/weapp-fnabstravel/commits/05aedd2857b2a5965cdf63de6654585a25ae3f17))
* no.2981575 解决比价页目的地和出发地传参 ([c87ef15](http://10.12.101.12/FNA/weapp-fnabstravel/commits/c87ef15bef39b54dc42366a7ee68a9bb70c3eef4))
* no.2981575 解决酒店列表渲染 ([0569fcc](http://10.12.101.12/FNA/weapp-fnabstravel/commits/0569fcc66ea3334465419a7b4d40d6ea3fbccbe6))


### Features

* no.2012081 新增商旅单点登录的功能 ([df48d84](http://10.12.101.12/FNA/weapp-fnabstravel/commits/df48d84d8939e59ab277afa0f815973fcbef31d8))
* no.2981575  新增商旅比价的需求 ([0372853](http://10.12.101.12/FNA/weapp-fnabstravel/commits/037285356c8b80cca5910dcf35c0a2d5ff0b39f7))
* no.2981575 新增往返 ([5800532](http://10.12.101.12/FNA/weapp-fnabstravel/commits/580053257f3ee61dd561701f720500055e56ff72))
* no.2981575 新增移动端icon ([b29a1df](http://10.12.101.12/FNA/weapp-fnabstravel/commits/b29a1df035575d2f747798289d8a082acac75e84))
* no.2981575 新增移动端icon ([7695e95](http://10.12.101.12/FNA/weapp-fnabstravel/commits/7695e9575cfc1c30e24506f842e6762968e94529))
* no.2981575 新增移动端比价列表 ([65ff95e](http://10.12.101.12/FNA/weapp-fnabstravel/commits/65ff95ef2f6f8cc02296eb8194dc6b49027ad9b5))
* no.2981575 新增移动端比价列表 ([ed6f472](http://10.12.101.12/FNA/weapp-fnabstravel/commits/ed6f472673b5c7d4f61919dc5129e7628a1225ff))
* no.2981575 新增获取服务商支持requestId ([1aceb24](http://10.12.101.12/FNA/weapp-fnabstravel/commits/1aceb246a3452c9b1bee9faec6f74ff695ea670b))


### Performance Improvements

* no.2981575 优化h5比价动画 ([938e57b](http://10.12.101.12/FNA/weapp-fnabstravel/commits/938e57b7a6efbf32a923f8977769e5e3d5b32f4d))
* no.2981575 优化h5比价动画 ([0565d98](http://10.12.101.12/FNA/weapp-fnabstravel/commits/0565d9850ae79022765b477b1c26f02af235e488))
* no.2981575 优化h5比价动画 ([cab460b](http://10.12.101.12/FNA/weapp-fnabstravel/commits/cab460b19edb848641f9a254461adf0534ea8f07))
* no.2981575 优化比价页传值 ([21c074d](http://10.12.101.12/FNA/weapp-fnabstravel/commits/21c074d02ae7e9d0918aef107b70617005a51a5d))
* no.2981575 优化比价页传值 ([a95abdd](http://10.12.101.12/FNA/weapp-fnabstravel/commits/a95abdd50e9d11d23f8ee8ddaae6f83f2271e8ab))
* no.2981575 优化移动端滚动 ([04b444c](http://10.12.101.12/FNA/weapp-fnabstravel/commits/04b444ca50ab85e33df1ff4896e70df2858141e7))



