{"name": "@weapp/fnabstravel", "version": "0.1.15", "dependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-angular": "^11.0.0", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@types/axios": "^0.14.0", "@types/form-data": "^2.5.0", "@types/jest": "^26.0.24", "@types/node": "^12.20.43", "@types/qs": "^6.9.7", "@types/react": "^17.0.10", "@types/react-dom": "^17.0.10", "@types/react-router-dom": "^5.3.3", "@weapp/base-css-vars": "^0.x", "@weapp/ecodesdk": "^0.x", "@weapp/layout": "^0.x", "@weapp/passport": "^0.x", "@weapp/scripts": "^0.x", "@weapp/ui": "^0.x", "@weapp/utils": "^0.x", "@weapp/vendor": "^0.x", "axios": "^0.21.4", "conventional-changelog-cli": "^2.2.2", "css-vars-ponyfill": "^2.4.7", "eslint-plugin-weapp": "0.0.3", "form-data": "^3.0.1", "husky": "^4.3.8", "loadjs": "^4.2.0", "mobx": "^4.15.7", "mobx-react": "^6.3.1", "qs": "^6.10.3", "react": "^17.0.2", "react-dom": "^17.0.2", "react-image-lightbox": "^5.1.4", "react-router-dom": "^5.2.17", "react-transition-group": "^4.4.5", "typescript": "^4.5.5", "web-vitals": "^0.2.4"}, "main": "build/fnabstravel/static/js/lib.js", "scripts": {"start": "weapp-scripts start", "build": "weapp-scripts build", "test": "weapp-scripts test --watchAll=false --passWithNoTests", "test:watch": "weapp-scripts test", "analyzer": "weapp-scripts build --analyzer", "upwedep": "weapp-scripts upwedep", "lib": "weapp-scripts lib", "pub": "weapp-scripts pub", "version": "conventional-changelog -p angular -i CHANGELOG.md -s", "props": "weapp-scripts props", "pub-kb": "weapp-scripts pub-kb", "postbuild": "weapp-scripts props"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "files": ["build", "lib", "es"], "types": "./lib/lib.d.ts", "husky": {"hooks": {"pre-commit": "npm run test", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "license": "MIT"}